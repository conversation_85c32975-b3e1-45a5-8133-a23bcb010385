import http from './index'
// import axios from 'axios';
// const mock = axios.create({
//   baseURL: 'https://yapi.int.ybm100.com/mock/4051', // 设置基本的请求URL
//   timeout: 5000, // 设置请求超时时间
// });

// // 添加请求拦截器
// mock.interceptors.request.use(
//   function (config) {
//     return config;
//   },
//   function (error) {
//     return Promise.reject(error);
//   }
// );

// mock.interceptors.response.use(
//   function (response) {
//     return response.data;
//   },
//   function (error) {
//     return Promise.reject(error);
//   }
// );
// 领取优惠券
export const receiveCoupon = (params) => {
	return http.postFormData('/merchant/center/voucher/receiveVoucher', params);
}
// 领券中心首页
export const receiveCenterIndex = (params) => {
	return http.postFormData('/merchant/center/voucher/receiveCenter/index', params);
}
// 领券中心首页-精选店铺券
export const shopSelect = (params) => {
	return http.postFormData('/merchant/center/voucher/receiveCenter/shopSelect', params);
}
// 领券中心店铺券
export const receiveCenterShopCoupon = (params) => {
	return http.postFormData('/merchant/center/voucher/receiveCenter/shopCoupon', params);
}
// 领券中心商品券
export const receiveCenterProductCoupon = (params) => {
	return http.postFormData('/merchant/center/voucher/receiveCenter/productCoupon', params);
}

// 连锁批量采购-刷新采购计划单
export const refreshPurchasePlan = (params) => {
	return http.post('/matchPrice/refreshPurchasePlan', params);
}

// 连锁批量采购-匹价列表编辑库存
export const updateStock = (params) => {
	return http.postFormData('/matchPrice/updateStock', params);
}

// 连锁批量采购-极速搜索
export const quickSearchProduct = (params) => {
	return http.post('/v2/matchPrice/quick-search-product', params);
}

// 连锁批量采购-比价规则店铺列表
export const matchPriceRuleShopList = (params) => {
	return http.get('/matchPrice/matchPriceRuleShopList', params);
}

// 连锁批量采购-查询匹配进度条
export const getMatchProgress = (params) => {
	return http.get(`/v2/matchPrice/match-progress?requestId=${params.requestId}`);
}

// 连锁批量采购-清空进度条
export const clearMatchProgress = (params) => {
	return http.get('/matchPrice/clearMatchProgress', params);
}

// 连锁批量采购-重新计算计算
export const selectMatchProduct = (params) => {
	return http.post('/matchPrice/selectMatchProduct', params);
}

// 连锁批量采购-获取埋点参数
export const getSowGroundParam = (params) => {
	return http.get('/snowGround/getParam', params);
}

// 连锁批量采购-加购
export const changeCartBatch = (params) => {
	return http.post('/merchant/center/cart/matchPrice/changeCartBatch', params);
}

// 连锁批量采购-下载模板
export const downloadTemplate = (params) => {
	return http.get('/fileTransfer/downloadTemplate', params);
}

// 导入采购单
export const importPurchasePlan = (params) => {
	return http.post('/matchPrice/importPurchasePlan', params);
}

//获取文件上传最大M和行数
export const getMaxMAndMaxRow = ()=>{
	return http.get('/v2/matchPrice/limit-setting');
}
// 下载报价单
export const downloadQuotation = (params) => {
	return http.post('/matchPrice/downloadQuotation', params, { responseType: 'blob' }, 'exportExcel');
}
// 连锁批量采购-下载失败的采购计划单
export const downloadWithPath = (params) => {
	return http.get('/fileTransfer/downloadWithPath', params, { responseType: 'blob' }, 'exportExcel');
}

// 结算
export const matchPrice = (params) => {
	return http.post('/merchant/center/order/matchPrice/preSettle', params);
}

// 我的账单
export const queryMyAccount = (params) => {
	return http.postFormData('/merchant/center/order/queryMyAccount', params);
}

// 采购总额（列表）
export const queryMyPurchaseOrderFee = (params) => {
	return http.postFormData('/merchant/center/order/queryMyPurchaseOrderFee', params);
}

// 采购总额（明细）
export const queryMyPurchaseOrderDetailFee = (params) => {
	return http.postFormData('/merchant/center/order/queryMyPurchaseOrderDetailFee', params);
}

// 实际退款（列表）
export const queryMyPurchaseRefundFee = (params) => {
	return http.postFormData('/merchant/center/order/queryMyPurchaseRefundFee', params);
}

// 实际退款（明细）
export const queryMyPurchaseRefunddetailFee = (params) => {
	return http.postFormData('/merchant/center/order/queryMyPurchaseRefunddetailFee', params);
}

// 退款中金额（列表）
export const queryMyPurchaseRefundingFee = (params) => {
	return http.postFormData('/merchant/center/order/queryMyPurchaseRefundingFee', params);
}

// 退款中金额（明细）
export const queryMyPurchaseRefundingdetailFee = (params) => {
	return http.postFormData('/merchant/center/order/queryMyPurchaseRefundingdetailFee', params);
}

// 下载对账单
export const downloadBill = (params) => {
	return http.postFormData('/merchant/center/order/purchase/download', params, { responseType: 'blob' }, 'exportExcel');
}

// 注册页面校验验证码
export const checkPhoCode = (params) => {
	return http.get('/login/checkPhoCode.htm', params);
}

// 注册页面发送验证码
export const sendRegisterVerificationCode = (params) => {
	return http.get('/validate/sendRegisterVerificationCode.json', params);
}

// 获取省市区
export const findAreaByParentId = (params) => {
	return http.get('/login/area/findAreaByParentId.json', params);
}

// 搜索要关联的店铺
export const searchShops = (params) => {
	return http.get('/pc/register/search', params);
}

// 审核中信息
export const getSimpleMerchantInfo = (params) => {
	return http.postFormData('/merchant/center/getSimpleMerchantInfo', params);
}

// 获取关联店铺
export const getMerchantList = (params) => {
	return http.postFormData('/merchant/center/account/merchantList', params);
}

// 切换登录店铺
export const selectMerchant = (params) => {
	return http.postFormData('/merchant/center/selectMerchant', params);
}

// 取消关联店铺
export const delRelShop = (params) => {
	return http.postFormData('/merchant/center/delRelShop', params);
}

// 店员列表
export const getClerk = (params) => {
	return http.postFormData('/merchant/center/clerk', params);
}

// 上传图片
export const uploadImg = (params) => {
	return http.postFormDataMultipart('/merchant/center/licenseAudit/uploadImg', params);
}

// 获取企业类型
export const getAllCustomerType = (params) => {
	return http.get('/merchantCustomerType/getCustomerType', params);
}

// 添加店铺
export const addShop = (params) => {
	return http.postFormData('/merchant/center/add', params);
}

// 账号注册
export const register = (params) => {
	return http.postFormData('/login/register_next', params);
}

// 关联店铺
export const relShop = (params) => {
	return http.postFormData('/merchant/center/relShop', params);
}

// 提交资质
export const uploadAuthorization = (params) => {
	return http.postFormData('/merchant/center/uploadAuthorization', params);
}

// 获取提交的提交资质
export const getUploadAuthorization = (params) => {
	return http.postFormData('/merchant/center/getUploadAuthorization', params);
}

// 获取当前登录店铺的信息
export const getLoginAccountInfo = (params) => {
	return http.postFormData('/merchant/center/getLoginAccountInfo', params);
}
// 获取当前订单催发货进度
export const history = (params) => {
	return http.get('/merchant/center/order/reminder/history', params);
}
// 撤销当前订单催发货
export const cancel = (params) => {
	return http.get('/merchant/center/order/reminder/cancel', params);
}
// 查询所有分类
export const getAllCategory = (params) => {
	return http.get('/getAllCategory', params);
}

// 大搜页各种分类接口
export const getAggs = (params) => {
	return http.postFormData('/pc/search/v1/aggs', params);
}
// 大搜页各种分类接口
export const getV2Aggs = (params) => {
	return http.postFormData('/pc/search/v2/aggs', params);
}

export const handleDiscount = (params) => {
	return http.postFormData('/marketing/discount/satisfactoryInHandPrice', params);
}

export const putRequest = (type, url, params) => { //data为键值对的post请求或者普通的get请求
	let tuc = type.toUpperCase();
	if (tuc == 'GET') {
		return http.get(url, params);
	} else if (tuc == 'POST') {
		return http.postFormData(url, params);
	}
};

// 大搜
export const getProductList = (params) => {
	return http.postFormData('/pc/search/v1/productList', params);
}
//运营位大搜
export const getProductLists = (params) => {
	return http.postFormData('/pc/search/v2/listProducts', params);
}

export const changeCart = (params) => {
	return http.postFormData('/merchant/center/cart/group/changeCart', params);
}
//组合购价格优惠
export const combinedInfoQuery = (params) => {
	return http.postFormData('/merchant/center/cart/combinedInfoQuery', params);
}
//商详获取组合购价格优惠
export const searchRecPurchase = (params) => {
	return http.postFormData('/pc/search/v2/searchRecPurchase', params);
}
//组合购前置接口
export const gotoSettle = (params) => {
	return http.postFormData('/merchant/center/order/group/preSettle.json', params);
}

// https://yapi.int.ybm100.com/project/65/interface/api/cat_8716 平安数字贷

//创建平安账户
export function createPingAnAccount(params){
	return http.post('/app/pinganaccount/createPingAnAccount', params)
}
export function createPingAnAccountNew(params){
	return http.post('/app/pinganaccount/boundCard', params)
}

//查询开户信息
export function queryPingAnAccountInfo(params){
	return http.get('/app/pinganaccount/queryPingAnAccountInfo', params)
}

//查询账户余额
export function queryPingAnAccountBalance(params){
	return http.get('/app/pinganaccount/queryPingAnAccountBalance', params)
}

//查询资金待清算银行信息
export function queryTopUpBankCardInfo(params){
	return http.get('/app/pinganaccount/queryTopUpBankCardInfo', params)
}

//查询银行列表
export function queryBankList(params){
	return http.get('/app/pinganaccount/queryBankList', params)
}

//查询平安开户银行支行列表
export function querySubBankList(params){
	return http.get('/app/pinganaccount/querySubBankList', params)
}

//验证平安账户
export function paymentAuth(params){
	return http.post('/app/pinganaccount/paymentAuth', params)
}

//查询企业信息
export function queryCompanyInfo(params){
	return http.get('/app/pinganaccount/queryCompanyInfo', params)
}

//发送验证码
export function sendVerificationCode(params){
	return http.get('/app/pinganaccount/sendVerificationCode', params)
}

// 获取爬虫验证码
export const sendCrawlerCode = (params) => {
	return http.get('/validate/sendCrawlerCode.json', params);
}

// 验证爬虫验证码
export const checkCrawlerCode = (params) => {
	return http.get('/validate/checkCrawlerCode.json', params);
}

// 药帮忙售后上传图片
export const uploadvoucherImg = (params) => {
	return http.postFormDataMultipart('/merchant/center/uploadFile/upload?uploadPath=/ybm/evidences/', params);
}
// 售后须知
export const queryServiceInfo = (params) => {
	return http.get('/app/afterSales/guide', params);
}
// 错票服务
export const queryInvoiceErrType = (params) => {
	return http.get('/app/afterSales/queryIncorrectInvoiceType', params);
}
// 获取专票信息
export const querySpecialInvoice = (params) => {
	return http.get('/app/afterSales/querySpecialInvoice', params);
}
// 发票售后提交
export const addInvoiceService = (params) => {
	return http.post('/app/afterSales/invoiceApply', params);
}
// 发票售后详情
export const invoiceServiceDetails = (params) => {
	return http.post('/app/afterSales/detail', params);
}
// 售后状态节点
export const queryProcessState = (params) => {
	return http.get('/app/afterSales/queryProcessState', params);
}
// 售后提交
export const saveOperate = (params) => {
	return http.post('/app/afterSales/saveOperate', params);
}
// 资质售后提交
export const addCredentialService = (params) => {
	return http.post('/app/afterSales/credentialApply', params);
}
// 资质选项服务
export const queryCredentialType = (params) => {
	return http.get('/app/afterSales/queryCredentialType', params);
}
// 资质商品列表
export const queryOrderList = (params) => {
	return http.get('/merchant/center/order/queryOrderDetailList', params);
}

/**
 * 智能采购 -> 获取表头
 * @param { FormData } params 
 * @returns 
 */
export const getTableHeaderInformation = (params) => {
	const result = {
		code: 1,
		success: true,
		fail: false,
		result: [
			''
		]
	}
	/* return Promise.resolve({
		code: 0,
		result: {
			headerInformation: ["1231", 'asd稍等', '21njkwan', '123jnjk', '123n 你撒旦艰苦', 'NSA尽可能'],
			rowNum: 12
		}
	}) */
	return http.post('/matchPrice/getTableInformation', params);
}
/**
 * 导入计划单
 * @param { FormData } params 
 * @returns 
 */
export const importPlan = (params) => {
	return http.post('/matchPrice/importPlan', params);
}
/**
 * 提交匹价
 */
export const submitMatchPrice =  (params) => {
	return http.post('/v2/matchPrice/match-price-submit', params);
}
/**
 * 查询比价结果
 */
export const inquireMatchPriceResult =  (params) => {
	return http.post(`/v2/matchPrice/match-price-result`,{...params});
}
/**
 * 获取规则
 */
export const getProcureMatchRule =  () => {
	return http.get('/v2/matchPrice/getProcureMatchRule');
}
/**
 * 保存规则
 * @param { object } params 
 */
export const saveMatchRule =  (params) => {
	return http.post('/v2/matchPrice/saveMatchRule', params);
}
/**
 * 查看商家
 */
export const lookMerchant = (params) =>{
	return http.post('/v2/matchPrice/shop-list', params);
}
/**
 * 保存加购/下单记录
 */
export const inquireRecording = (params) =>{
	return http.post('/v2/matchPrice/conversion', params);
}
/**
 * 下载失败计划单
 */
export const exportFailedPlan = (params) => {
	return http.post('/matchPrice/exportFailedPlan', params, { responseType: 'blob' })
}
export const downloadQuotationV2 = (params) => {
	return http.postV2('/v2/matchPrice/downloadQuotation', params, { responseType: 'blob'})
}
/**
 * 获取表头
 * @param {Object} params 
 * @returns 
 */
export const downloadQuotationV3 = (params) => {
	return http.post('/v2/matchPrice/getTableInformation', params);
}

/**
 * 导入接口
 * @param {Object} params 
 * @returns 
 */
export const matchPriceImport = (params) => {
	return http.post('/v2/matchPrice/import', params);
}

/**
 * 缺少字商品修改保存接口
 * @param {Object} params 
 * @returns 
 */
export const matchPricedDtailUpdate = (params) => {
	return http.post('/v2/matchPrice/detail/update', params);
}

/**
 * 大搜商品
 * @param {Object} params 
 * @returns 
 */
export const bigSearchGoods = (params)=>{
	return http.post('/v2/matchPrice/es-search-product', params); 
}
/**
 * sku替换
 * @param {Object} params 
 * @returns 
 */
export const skuReplace = (params)=>{
	return http.post('/v2/matchPrice/replaceSku', params); 
}
/**
 * 二次模糊匹配
 * @param {Object} params 
 * @returns 
 */
export const secondMatch = (params)=>{
	return http.post('/v2/matchPrice/secondMatchSubmit', params); 
}
//校验支付密码
export const payPwdQueryState = (params) => {
	return http.post('/merchant/center/payPwd/queryState', params);
}
//密码是否正确
export const centerCheckPayPwd = (params) => {
	return http.post('/merchant/center/checkPayPwd', params);
}
//转入购物金

export const goldTransferIn = (params) => {
	return http.postFormData('/pc/virtual/gold/transferIn', params);
}
//流水号
export const getTranNo = (params) => {
	return http.post('/pc/virtual/gold/getTranNo', params);
}
//PC查询转钱结果
export const getResultByTranNo = (params) => {
	return http.postFormData('/pc/virtual/gold/getResultByTranNo', params);
}
//记录点击隐私协议
export const logger = (params) => {
	return http.post('/loginAgreement/addLog', params);
}
//获取协议信息
export const getAgreement = () => {
	return http.get('/loginAgreement/getLoginAgreement');
}
//平台代收款说明
export const getPaymentDescPC = (params) => {
	return http.post('/merchant/center/order/agreement/instructions', params);
}
// PC-平安商户明细
export const getMerchantDetail = (params) => {
	return http.post('/app/pinganaccount/queryPayRecordsForPc', params);
}
//PC-平安银行交易类型
export const getPayType = () => {
	return http.post('/app/pinganaccount/queryFbankTradeCode');
}
//PC-平安商户明细导出
export const exportPingAnMerchantExcel = (params) => {
	return http.post('/app/pinganaccount/exportExcel', params, { responseType: 'blob' }, 'exportExcel');
}

// PC-购物金变动类型
export const getGoldChangeType = () => {
	return http.post('/pc/virtual/gold/queryVirtualGoldChangeType');
}
//PC-我的购物金流水
export const getGoldFlow = (params) => {
	return http.post('/pc/virtual/gold/queryMyRecord', params);
} 

//PC-购物金变动导出
export const exportGoldFlowExcel = (params) => {
	return http.post('/pc/virtual/gold/exportExcel', params, { responseType: 'blob' }, 'exportExcel');
}
//解绑
export const unBoundCard = (params) => {
	return http.post('/app/pinganaccount/unBoundCard', params);
}

//查看优惠信息
export const satisfactoryInHandPriceWithDetail = (params) => {
	return http.post('/marketing/discount/satisfactoryInHandPriceWithDetail', params);
}