import { createApp } from 'vue'
import App from './App.vue'
import 'normalize.css' // css初始化
import "@/assets/scss/common.css"
import VConsole from "vconsole/dist/vconsole.min.js";
import lazyPlugin from 'vue3-lazy'
import store from './store'
import router from './router'
import Bridge from "@/config/Bridge";
import "@/config/flex";
import "@/config/zhuge-io";
import { trackingIdentify, actionTracking } from '@/config/eventTracking';
import goods_img from "@/assets/images/goods.png";
import { getUrlParam, getCookieValue } from '@/utils/index';
import ElementPlus from 'element-plus'// 1. 引入你需要的组件
import 'element-plus/dist/index.css'
import AnalysysAgent from '@/config/AnalysysAgent_JS_SDK.es6.min.js'
import { <PERSON><PERSON>,Picker, Field, Popup, Icon,Dialog, Cell, CellGroup, Loading,PasswordInput,NumberKeyboard,Tab, Tabs,List,Uploader,Calendar   } from 'vant';
import exposedDirective from '@/utils/expose'
import exposedCombination from '@/utils/exposeCombination'
// 2. 引入组件样式
import 'vant/lib/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// 引入mock文件
import "./mock"; // mock 方式，正式发布时，注释掉该处即可

import * as ElIconModules from '@element-plus/icons-vue'
// import 'element-plus/lib/theme-chalk/index.css'
 

const app = createApp(App);
app.directive('in-viewport', exposedDirective);//注册
app.directive('exposure-combination', exposedCombination);



// 原生调用js的桥
window._self_fn = {
  isTimeCalibration: () => {},
  isShareSuccess: () => {},
  successCallBack: () => {},
  errorCallBack: () => {},
};

// 获取会员Id
const urlMerchantId = getUrlParam('merchantId');
app.config.globalProperties.$merchantId = urlMerchantId;
localStorage.setItem("merchantId", urlMerchantId);
console.log("setmerchartId");
Bridge.getMerchantId(function(merchantId, accountId) {
  if (merchantId) {
    localStorage.setItem('merchantId', merchantId);
    app.config.globalProperties.$merchantId = merchantId;
  }
  localStorage.setItem('accountId', accountId);
});

const BASEURL_PC = import.meta.env.VITE_BASE_URL_PC;
if (BASEURL_PC.indexOf(window.location.host) > -1) {
  let merchantId = '0';
  const isSelfWindow = window.parent === window;
  merchantId = getUrlParam('merchantId');
  if (merchantId === '0') {
    if (!isSelfWindow) {
      merchantId = (window.parent.document.getElementById('merchantId') || {}).value || "0";
    } else {
      merchantId = (window.document.getElementById('merchantId') || {}).value || "0";
    }
  }
  app.config.globalProperties.$merchantId = merchantId;
  localStorage.setItem("merchantId", merchantId);
  // if (isSelfWindow) {
  //   trackingIdentify(localStorage.getItem('merchantId'));
  // }
} else {
  trackingIdentify(localStorage.getItem('merchantId'));
}

console.log('环境-测试 stage', process.env.NODE_ENV);
window.evn = process.env.NODE_ENV;
// 非线上环境，加载 VConsole
if (process.env.NODE_ENV !== 'production') {
  new VConsole();
}
var cookie = getCookieValue("xyy_principal") || "";
var accountId = "";
var cookieMerchantId= "";
var cookieArr = cookie.split("&");
if (cookieArr.length > 2) {
    cookieMerchantId = cookieArr[2];
    accountId = cookieArr[0];
    localStorage.setItem("merchantId", cookieMerchantId);
}
window.AnalysysAgent.registerSuperProperty("merchant_id", cookieMerchantId || "");
window.AnalysysAgent.registerSuperProperty("account_id", accountId || "");
AnalysysAgent.init({
  // appkey: 'e6f27194adb12753',//测试APPKEY
  appkey: 'a775ff9e4e84bc9a', //预发/线上APPKEY
  uploadURL: 'https://jgmd.ybm100.com',//上传数据的地址
  debugMode: 0, // 1 开启调试模式且数据不入库  2 开启调试模式且数据入库 0 关闭调试模式
  autoPageViewDuration: true,
})

// 注册 (功能指令)
app.directive('tracking', (el, binding) => {
  const value = binding.value;
    // 行为上报
    el.addEventListener('click', () => {
      actionTracking(value.eventName, value.params)
    }, false);
})
app.use(lazyPlugin, {
  loading: goods_img, // 加载时默认图片
  error: goods_img, // 图片失败时默认图片
})
app.use(store)
app.use(router)
app.use(Button)
app.use(Picker)
app.use(Field)
app.use(Popup)
app.use(Icon)
app.use(Dialog)
app.use(Cell)
app.use(CellGroup)
app.use(Loading)
app.use(PasswordInput)
app.use(NumberKeyboard)
app.use(Tab);
app.use(Tabs);
app.use(List);
app.use(Uploader)
app.use(Calendar );
app.use(ElementPlus, {
  locale: zhCn,
})
Object.keys(ElIconModules).forEach(function (key) {
  app.component(ElIconModules[key].name, ElIconModules[key])
})
app.config.globalProperties.scmEActive=function(num){
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < num; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result
}
app.config.globalProperties.keepChineseEnglishAndNumbers=function(text) {
  text=String(text)
  return text.replace(/[._\-~|@]/g, "");
}
app.mount('#app')


