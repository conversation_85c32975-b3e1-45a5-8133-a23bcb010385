// src/directives/v-visible.js

// 存储待曝光的元素信息
const pendingExposures = new Map();

// 检查是否有遮罩层显示
function hasMaskLayer() {
  // 检查常见的遮罩层选择器
  const maskSelectors = [
    '.show-mask',
    '.el-overlay',
    '.el-dialog__wrapper',
    '.el-drawer__wrapper',
    '[class*="mask"]',
    '[class*="overlay"]'
  ];

  for (const selector of maskSelectors) {
    const maskElements = document.querySelectorAll(selector);
    for (const maskEl of maskElements) {
      const style = window.getComputedStyle(maskEl);
      if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
        return true;
      }
    }
  }
  return false;
}

// 处理待曝光队列
function processPendingExposures() {
  if (hasMaskLayer()) {
    return; // 如果还有遮罩层，继续等待
  }

  // 遮罩层已关闭，处理所有待曝光的元素
  pendingExposures.forEach((exposureData, el) => {
    // 再次检查元素是否仍在视口中
    const rect = el.getBoundingClientRect();
    const isInViewport = rect.top < window.innerHeight &&
                        rect.bottom > 0 &&
                        rect.left < window.innerWidth &&
                        rect.right > 0;

    if (isInViewport) {
      try {
        exposureData.fn(el, exposureData.data);
      } catch (error) {
        console.error('曝光回调执行错误:', error);
      }
    }
  });

  // 清空待曝光队列
  pendingExposures.clear();
}

// 监听遮罩层变化
let maskObserver = null;
function startMaskObserver() {
  if (maskObserver) return;

  maskObserver = new MutationObserver(() => {
    // 延迟检查，确保DOM更新完成
    setTimeout(processPendingExposures, 100);
  });

  maskObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['style', 'class']
  });
}

export default {
  mounted(el, binding) {
    const options = {
      root: null, // 使用视口作为根
      threshold: 0.1 // 当至少 10% 的元素在视口中时触发回调
    };

    const observer = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          observer.unobserve(el);

          // 检查是否有遮罩层
          if (hasMaskLayer()) {
            // 有遮罩层，将曝光信息存储到待处理队列
            pendingExposures.set(el, {
              fn: binding.value.fn,
              data: binding.value.data
            });

            // 启动遮罩层监听器
            startMaskObserver();
          } else {
            // 没有遮罩层，立即曝光
            try {
              binding.value.fn(el, binding.value.data);
            } catch (error) {
              console.error('曝光回调执行错误:', error);
            }
          }
        }
      });
    }, options);

    observer.observe(el);

    // 存储observer到元素上，便于清理
    el._exposureObserver = observer;
  },

  unmounted(el) {
    // 清理observer
    if (el._exposureObserver) {
      el._exposureObserver.disconnect();
      delete el._exposureObserver;
    }

    // 从待曝光队列中移除
    pendingExposures.delete(el);
  }
};
