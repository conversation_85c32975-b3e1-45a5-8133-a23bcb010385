import { ref, reactive, defineComponent, h } from 'vue';
import lGuide from "./l-guide.vue";
import lGuideStep from "./l-guide-step.vue";

export const guideCreater = () => {
  const stepElList = reactive([]);

  const lGuideComp = defineComponent({
    props: {
      start: {
        type: Boolean,
        default: false
      }
    },
    setup(props, { emit, slots }) {
      const step = ref(1);

      const onEnd = () => {
        console.log(666);
        emit('end');
      };

      const onNext = (newStep) => {
        if (step.value !== newStep) {
          step.value = newStep;
          emit('next', newStep);
        }
      };

      return () => h(lGuide, {
        start: props.start,
        stepElList: stepElList,
        step: step.value,
        onEnd,
        onNext
      }, {
        default: () => slots.default?.(),
        control: () => slots.control?.(stepElList[step.value - 1]),
        jump: () => slots.jump?.(),
        pre: () => slots.pre?.(),
        next: () => slots.next?.(),
        end: () => slots.end?.(),
      });
    }
  });

  const lGuideStepComp = defineComponent({
    props: {
      step: {
        type: Number,
        required: true
      },
      title: {
        type: String,
        default: ''
      },
      content: {
        type: String,
        default: ''
      }
    },
    emits: ['getStep'],
    setup(props, { emit, slots }) {
      const onGetStep = (data) => {
        stepElList[data.step - 1] = {
          ref: data.ref,
          title: data.title,
          content: data.content,
        };
      };

      return () => h(lGuideStep, {
        step: props.step,
        title: props.title,
        content: props.content,
        onGetStep
      }, slots.default?.());
    }
  });

  return {
    lGuide: lGuideComp,
    lGuideStep: lGuideStepComp
  };
};
