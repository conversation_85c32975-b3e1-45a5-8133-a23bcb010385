<script setup>
import { ref, onMounted, defineEmits, defineProps } from 'vue';

const props = defineProps({
  step: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['getStep']);

const random = ref(Math.random());

onMounted(() => {
  emit('getStep', {
    step: props.step,
    ref: guideRef.value,
    title: props.title,
    content: props.content,
  });
});

const guideRef = ref(null);
</script>

<template>
  <div>
    <div style="display: inline-block;" ref="guideRef">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
</style>
