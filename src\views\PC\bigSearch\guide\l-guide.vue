<script setup>
import { ref, reactive, watch, computed, onMounted, nextTick, defineEmits, defineProps } from 'vue';

const props = defineProps({
  start: {
    type: Boolean,
    default: false
  },
  stepElList: {
    type: Array,
    default: () => []
  },
  step: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['end', 'next']);

const target = ref(false);

const focusEl = reactive({
  width: '',
  height: '',
  top: '',
  left: '',
});

const controlEl = reactive({
  top: '',
  left: '',
});

let resizeTimeout = null;

watch(() => props.start, (val) => {
  console.log(props.stepElList, '----------------------------');
  target.value = val;
  if (val && props.stepElList.length > 0) {
    document.getElementsByTagName('html').item(0).style.overflow = 'hidden';
    startNow(0);
  } else {
    document.getElementsByTagName('html').item(0).style.overflow = '';
  }
});

watch(() => props.step, (val) => {
  startNow(val - 1);
});


function startNow(step) {
  if (!target.value || !props.stepElList[step]) return;
  const elPositionData = props.stepElList[step].ref.getBoundingClientRect();
  focusEl.width = elPositionData.width + 20 + 'px';
  focusEl.height = elPositionData.height + 10 + 'px';
  focusEl.top = elPositionData.top - 5 + 'px';
  focusEl.left = elPositionData.left - 10 + 'px';
  nextTick(() => {
    controlMove(elPositionData);
  });
}

function clear() {
  focusEl.width = '';
  focusEl.height = '';
  focusEl.top = '';
  focusEl.left = '';
}

function controlMove(elPositionData) {
  const { width, height, top, left } = elPositionData;
  const ran = randomRef.value?.getBoundingClientRect();
  if (!ran) return;
  // 上下左右哪个地方差值最大就放哪
  // 上 左 右 下
  const arr = [
    top * window.innerWidth,
    left * window.innerHeight,
    (window.innerWidth - width - left) * window.innerHeight,
    (window.innerHeight - height - top) * window.innerWidth
  ];
  let maxIndex = 0;
  arr.forEach((val, i) => {
    if (val > arr[maxIndex]) {
      maxIndex = i;
    }
  });

  if (maxIndex === 0) {
    controlEl.top = top - ran.height - 10 + 'px';
    controlEl.left = window.innerWidth - width - left > left ? left - 10 : left + width - ran.width + 10;
    controlEl.left = controlEl.left + "px";
  } else if (maxIndex === 1) {
    controlEl.left = left - ran.width - 15 + 'px';
    controlEl.top = window.innerHeight - height - top > top ? top - 5 : top + height - ran.height + 5;
    controlEl.top = controlEl.top + "px";
  } else if (maxIndex === 2) {
    controlEl.left = left + width + 15 + 'px';
    controlEl.top = window.innerHeight - height - top > top ? top - 5 : top + height - ran.height + 5;
    controlEl.top = controlEl.top + "px";
  } else if (maxIndex === 3) {
    controlEl.top = top + height + 10 + 'px';
    controlEl.left = window.innerWidth - width - left > left ? left - 10 : left + width - ran.width + 10;
    controlEl.left = controlEl.left + "px";
  }
}

const randomRef = ref(null);

onMounted(() => {
  window.addEventListener('resize', () => {
    if (resizeTimeout) clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      startNow(props.step - 1);
    }, 200);
  });
});
</script>

<template>
  <div>
    <div>
      <slot></slot>
    </div>
    <div style="transition: all 0.3s;mix-blend-mode: hard-light;" class="l-mask" v-show="start && props.stepElList.length > 0">
      <div class="l-focus-el" :style="focusEl">
      </div>
    </div>
    <div v-show="start && props.stepElList.length > 0" class="l-control" ref="randomRef" :style="controlEl">
        <div style="position: relative;margin-bottom: 10px;">
          <slot name="control" :item="stepElList[step - 1]">
          </slot>
        </div>
        <div class="l-footer" style="display: flex;">
          <!-- <button class="l-btn" @click="$emit('end')">跳过</button>
          <button v-if="step > 1" class="l-btn" @click="$emit('next', step - 1)">上一步</button>
          <button v-if="step < stepElList.length" class="l-btn" @click="$emit('next', step + 1)">下一步</button>
          <button v-if="step === stepElList.length" class="l-btn" @click="$emit('end')">确定</button> -->
          <span>
            <slot name="jump"></slot>
          </span>
          <span v-if="step > 1">
            <slot name="pre"></slot>
          </span>
          <span v-if="step < stepElList.length">
            <slot name="next"></slot>
          </span>
          <span v-if="step === stepElList.length">
            <slot name="end"></slot>
          </span>
        </div>
      </div>
    <!-- <div :style="focusEl" style="z-index:10002;position: fixed;"></div> -->
  </div>
</template>

<style scoped>
p {
  margin: 5px 0;
}
.l-focus-el {
  position: fixed;
  background-color: white;
  border-radius: 7px;
  transition: all 0.3s;
  mix-blend-mode: color;
}
.l-control {
  position: fixed;
  background-color: white;
  border-radius: 5px;
  padding: 5px 10px;
  width: 100%;
  max-width: 300px;
  z-index: 10000;
  top: 100px;
  transition: all 0.3s;
}
.l-btn {
  white-space: nowrap;
}
.l-transition {
  width: 100%;
  max-width: max-content;
  transition: all 0.3s;
}
.l-footer {
  justify-content: end;
}
.l-mask {
  position: fixed;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background: rgba(0,0,0,.5)
}
</style>
