<template>
    <div style="background:#FAFAFA">
        <div v-if="isHidden" class="wrapperBox" v-loading="loading" element-loading-text="" element-loading-spinner="none">
            <div v-if="loading" class="loading-overlay">
                加载中...
            </div>
            <div class="top">
                <div class="middle">
                    <img style="width:18px;height:18px;" src="../../../assets/images/search/combination-top-new.png" alt="top">
                </div>
                <div class="fox">{{title}}</div>
            </div>
            <div class="main">
                <!-- 添加左侧箭头 -->
                <div class="custom-arrow custom-arrow--left" @click="prevSlide">
                    <i class="el-icon-arrow-left"></i>
                </div>
                
                <el-carousel 
                    ref="carouselRef" 
                    height="210px" 
                    :interval="carouselTime" 
                    @change="carouselChange" 
                    :arrow="never"
                    indicator-position="none"
                    :autoplay="autoplay"
                    v-if="subsidiaryDataList.length">
                    <el-carousel-item v-for="(item) in subsidiaryDataList" :key="item">
                        <div class="carousel-content">
                            <div class="combinationLeft" :key="subsidiaryData?.id" v-in-viewport="{fn:exposureViewMain,data:{id:mainData?.id,name:mainData.showName,rank:mainData.rank,subRank:mainData.subRank,qt_sku_data:mainData['qtSkuData']}}">
                    <a @click="toDetail(mainData?.id, mainData, 1);combinationQt(mainData)">
                        <div class="images">
                            <div>
                                <img
                                class="pic"
                                v-lazy="imgUrl + '/ybm/product/min/' + mainData?.imageUrl"
                                :key="mainData?.imageUrl"
                                :alt="mainData?.showName"
                                />
                            </div>
                        </div>
                    </a>
                    <div class="leftRight">
                        <div>
                            <div class="showName">
                                <div class="commonName">
                                    <!-- 药名和规格 -->
                                    <span class="name">{{ mainData?.showName?.trim()}}{{ mainData?.spec}}</span>
                                </div>
                            </div>
                            <div class="manufacturer textellipsis">{{ mainData?.manufacturer }}</div>
                            <div
                                v-if="mainData?.nearEffect || (mainDat?.tags.purchaseTags || []).filter(i=>i.type==4).length"
                                class="nearEffectBox"
                                :class="{leftText: (mainData?.tags.purchaseTags || []).filter(i=>i.type==4).length}"
                                >
                                <span v-if="mainData?.nearEffect">有效期：{{ mainData?.nearEffect }}</span>
                                <span class="countTag" v-if="(mainData?.tags.purchaseTags || []).filter(i=>i.type==4).length">{{ ((mainData?.tags.purchaseTags || []).filter(i=>i.type==4)[0] || {}).text }}</span>
                            </div>
                        </div>
                        <div>
                            <div class="info" >
                                <!-- 价格  -->
                                <div class="price-wrap">
                                    <div class="pricewapper">
                                        <div class="price-box clearfixed">
                                            <div class="price-two price-mian">
                                            <p>
                                                <span class="priceDec">￥</span>
                                                <span  v-if="mainData?.actPt?.amount">
                                                    <span class="priceInt">{{String(mainData?.actPt.amount.toFixed(2)).split('.')[0]}}</span>
                                                    <span class="priceInt">.{{String(mainData?.actPt.amount.toFixed(2)).split('.')[1] || '00'}}</span>
                                                </span>
                                            </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="content">
                                <div class="spread-add">
                                    <div class="reduce" @click.stop.prevent="addProductCart('min',1);combinationQtClick(mainData,'减',1)">-</div>
                                    <div class="cont">
                                    <input
                                        @click.stop.prevent="combinationQtClick(mainData,mianCount,2)"
                                        class="input-val"
                                        type="tel"
                                        v-model="mianCount"
                                        @change="inputCart($event,1)"
                                    />
                                    </div>
                                    <div class="plus" @click.stop.prevent="addProductCart('add',1);combinationQtClick(mainData,'加',3)">+</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="middle">
                    <img style="width:24px;height:24px;" src="../../../assets/images/search/combination-plus.png" alt="plus">
                </div>
                <div class="combinationCenter" :key="subsidiaryData?.id" v-in-viewport="{fn:exposureViewDeputy,data:{id:subsidiaryData?.id,name:subsidiaryData.showName,rank:subsidiaryData.rank,subRank:subsidiaryData.subRank,qt_sku_data:subsidiaryData['qtSkuData']}}">
                    <a @click="toDetail(subsidiaryData?.id, subsidiaryData, 2);combinationQt(subsidiaryData)" >
                        <div class="images">
                            <div>
                                <img
                                class="pic"
                                v-lazy="imgUrl + '/ybm/product/min/' + subsidiaryData?.imageUrl"
                                :key="subsidiaryData?.imageUrl"
                                :alt="subsidiaryData?.showName"
                                />
                            </div>
                            <!-- <div class="activity-token-combination" v-if="discount">
                                <div>组合购省{{discount}}元</div>
                            </div> -->
                        </div>
                    </a>
                    <div class="leftRight">
                        <div>
                            <div class="showName">
                                <div class="commonName">
                                <!-- 药名和规格 -->
                                <span class="name">{{ subsidiaryData?.showName?.trim() }}{{ subsidiaryData?.spec }}</span>
                                </div>
                            </div>
                            <div class="manufacturer textellipsis">{{ subsidiaryData?.manufacturer }}</div>
                            <div
                                v-if="subsidiaryData?.nearEffect || (subsidiaryData?.tags?.purchaseTags || []).filter(i=>i.type==4).length"
                                class="nearEffectBox"
                                :class="{leftText: (subsidiaryData?.tags?.purchaseTags || []).filter(i=>i.type==4).length}"
                                >
                                <span v-if="subsidiaryData?.nearEffect">有效期：{{ subsidiaryData?.nearEffect }}</span>
                                <span class="countTag" v-if="(subsidiaryData?.tags?.purchaseTags || []).filter(i=>i.type==4).length">{{ ((subsidiaryData?.tags.purchaseTags || []).filter(i=>i.type==4)[0] || {}).text }}</span>
                            </div>
                        </div>
                        <div>
                            <div class="info" >
                                <!-- 价格  -->
                                <div class="price-wrap">
                                    <div class="pricewapper">
                                        <div class="price-box clearfixed">
                                            <div class="price-two price-sub">
                                                <p>
                                                    <span class="priceDec">￥</span>
                                                    <span v-if="subsidiaryData?.actPt?.amount">
                                                        <span class="priceInt">{{String(subsidiaryData?.actPt?.amount.toFixed(2)).split('.')[0]}}</span>
                                                        <span class="priceInt">.{{String(subsidiaryData?.actPt?.amount.toFixed(2)).split('.')[1] || '00'}}</span>
                                                    </span>
                                                </p>
                                                <div class="price-dec" v-if="discount">
                                                    <span class="title">立省</span>
                                                    <span class="">￥{{ discount }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="content">
                                <div class="spread-add">
                                    <div class="reduce" @click.stop.prevent="addProductCart('min',2);combinationQtClick(subsidiaryData,'减',1)">-</div>
                                    <div class="cont">
                                    <input
                                        @click.stop.prevent="combinationQtClick(subsidiaryData,subsidiaryCount,2)"
                                        class="input-val"
                                        type="tel"
                                        v-model="subsidiaryCount"
                                        @change="inputCart($event,2)"
                                    />
                                    </div>
                                    <div class="plus" @click.stop.prevent="addProductCart('add',2);combinationQtClick(subsidiaryData,'加',3)">+</div>
                                </div>
                                <!-- <div style="display:flex;align-items: center;user-select: none;width: 70px;height: 17px;justify-content: end;margin-right: 5px;" v-if="showFlushed" @click="changeProduct">
                                    <img style="width:14px;height:14px;" src="../../../assets/images/search/combination-flushed.png" alt="plus">
                                    <div style="color: #666666;margin-left:3px;">换一个</div>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
                        </div>
                    </el-carousel-item>
                </el-carousel>
                <!-- 添加右侧箭头 -->
                <div class="custom-arrow custom-arrow--right" @click="nextSlide">
                    <i class="el-icon-arrow-right"></i>
                </div>
                <!-- 自定义指示器 -->
                <div class="custom-indicators">
                    <span 
                        v-for="(item, index) in subsidiaryDataList" 
                        :key="index"
                        :class="{ active: currentIndex === index }"
                        @click="setActiveItem(index)">
                    </span>
                </div>
                <div class="combinationRight">
                    <div class="preferential" >
                        <div class="item">为您省：</div>
                        <div class="item" v-if="combinedDiscountSub">￥{{combinedDiscountSub}}</div>
                    </div>
                    <div class="arrivalPrice">
                        <div class="item">到手价：</div>
                        <div class="price-wrap">
                            <div class="pricewapper">
                                <div class="price-box clearfixed">
                                    <div class="price-two">
                                    <p>
                                        <span class="priceDec">￥</span>
                                        <span v-if="realPay">
                                            <span class="priceInt">{{String(realPay.toFixed(2)).split('.')[0]}}</span>
                                            <span class="priceInt">.{{String(realPay.toFixed(2)).split('.')[1] || '00'}}</span>
                                        </span>
                                    </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="combinationBuy"   @click="combinationBuyClick();combinationModuleClick(mainData)">立即购买</div>
                </div>
            </div>
        </div>
    </div>
</template>

  
<script setup>
  import { onMounted, ref, getCurrentInstance, onUnmounted, nextTick, reactive, computed } from "vue";
  import { ElMessage } from "element-plus";
  import { Search } from "@element-plus/icons-vue";
  import { changeCart,combinedInfoQuery,searchRecPurchase,gotoSettle} from "@/http_pc/api";
  import { useStore } from "vuex";
  import  install from "@/utils/qt"
  install()
  const imgUrl = import.meta.env.VITE_IMG;
  const { proxy } = getCurrentInstance();
  const store = useStore();
  const merchantId = proxy.$merchantId;
  let mianCount = ref('');
  let subsidiaryCount = ref('');
  const mainData = ref('');
  const med_num = ref('');
  const is_split = ref('');
  const subsidiaryDataList = ref([]);
  const subsidiaryData = ref('');
  const fristSubsidaryDataSkuId = ref('');
  const combinedDiscountSub = ref(0.00);
  const discount = ref('0.00');
  const realPay = ref(0.00);
  const loading = ref(false);
  const detailUrl = import.meta.env.VITE_BASE_URL_PC;
  const scmE = ref('');
  const currentIndex = ref(0);
  const exposedProducts = new Set();
  /**组合购主品曝光 */
  const exposureViewMain = (el,data) => {
    const exposureKey = `${data.id}-${currentIndex.value}`;
    if(exposedProducts.has(exposureKey)) {
        return
    }
    exposedProducts.add(exposureKey);
    if(window.parent.getSpmE && scmE.value && fristSubsidaryDataSkuId.value == subsidiaryData.value.id) {
        let params={
        "spm_cnt":`1_4.productDetail_${proxy.$route.query.id}-<EMAIL>@${data.rank}_prod@${data.rank}-${data.subRank}.${window.parent.getSpmE()}`,
        "scm_cnt":`search.0.all_0.prod-${data.id}.${scmE.value}`,
        "product_id": data.id,
        "product_name":data.name,
        'qt_sku_data': data.qt_sku_data

      }
        aplus_queue.push({
              'action': 'aplus.record',
              'arguments': ['page_list_product_exposure', 'EXP',params]
      });
    }
  };
  /**组合购副品曝光 */
  const exposureViewDeputy = (el,data) => {
    const exposureKey = `${data.id}-${currentIndex.value}`;
    if(exposedProducts.has(exposureKey)) {
        return
    }
    exposedProducts.add(exposureKey);
    if(window.parent.getSpmE && scmE.value){
        let params={
        "spm_cnt":`1_4.productDetail_${proxy.$route.query.id}-<EMAIL>@${data.rank}_prod@${data.rank}-${data.subRank}.${window.parent.getSpmE()}`,
        "scm_cnt":`search.0.all_0.prod-${data.id}.${scmE.value}`,
        "product_id": data.id,
        "product_name":data.name,
        'qt_sku_data': data.qt_sku_data
      }
        aplus_queue.push({
              'action': 'aplus.record',
              'arguments': ['page_list_product_exposure', 'EXP',params]
      });
    }
  };
  /**点击埋点 */
    const combinationQt = (data,scme) => {
      if(window.parent.getSpmE && scmE.value){
        let ScmE=scme || scmE.value + proxy.scmEActive(6)
        let params={
            "spm_cnt":`1_4.productDetail_${proxy.$route.query.id}-<EMAIL>@${data.rank}_prod@${data.rank}-${data.subRank}.${window.parent.getSpmE()}`,
            "scm_cnt":`search.0.all_0.prod-${data.id}.${ScmE}`,
            "product_id": data.id,
            "product_name":data.showName,
            'qt_sku_data': data.qtSkuData
        }
        console.log(params,'action_list_product_click')
        aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['action_list_product_click', 'CLK',params]
        });
      }
    }
  /**点击按钮埋点 */
    const combinationQtClick = (data,text,index) => {
      if(window.parent.getSpmE && scmE.value){
        let ScmE=scmE.value + proxy.scmEActive(6)
        let params={
            "spm_cnt":`1_4.productDetail_${proxy.$route.query.id}-<EMAIL>@${data.rank}_prod@${data.rank}-${data.subRank}_btn@${index}.${window.parent.getSpmE()}`,
            "scm_cnt":`search.0.all_0.prod-${data.id}_text-${text}.${ScmE}`,
            "product_id": data.id,
            "product_name":data.showName,
            'qt_sku_data': data.qtSkuData
        }
        combinationQt(data,ScmE)
        console.log(params,'action_product_button_click')
        aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['action_product_button_click', 'CLK',params]
        });
      }
    }
    /**组合购模块点击*/
  const combinationModuleClick=(data)=>{
    if(scmE.value && window.parent.getSpmE ){ //
      let ScmE=scmE.value + proxy.scmEActive(6)
      let qtSkuData = JSON.parse(data.qtSkuData)
      let qt = {
        list_position_type_name: qtSkuData.list_position_type_name,
        main_product_id: qtSkuData.main_product_id,
        main_product_name: qtSkuData.main_product_name,
        list_position_type:qtSkuData.list_position_type,
        scm_id: scmE.value,
        sub_product_id_groups: [String(subsidiaryData.value.id)],
        sub_product_name_groups: [subsidiaryData.value.showName],
        sub_product_sub_rank_groups: [String(subsidiaryData.value.subRank)],
        sub_product_num: subsidiaryCount.value > 0 ? 1 : 0,
      }
      let params={
        "spm_cnt":`1_4.productDetail_${proxy.$route.query.id}-<EMAIL>@${data.rank}_btn@1.${window.parent.getSpmE()}`,
        "scm_cnt":`search.0.all_0.prod-${data.id}_text-立即购买.${proxy.scmEActive(14)}`,
        'qt_sku_data': JSON.stringify(qt)
      }
            console.log(params,'action_sub_module_click')
      aplus_queue.push({
        'action': 'aplus.record',
        'arguments': ['action_sub_module_click', 'CLK',params]
      });
    }
  }
  const isHidden = ref(true);
  const title = ref('');
  const showFlushed = ref(true); //隐藏换一个
  const submitDisable = ref(false); //防抖
    const toDetail = (id, item, type) => {
      window.open(`${detailUrl}search/skuDetail/${id}.htm`);
    }
  const combinationBuyClick = () => {
    if(mainData.value.actPt.selectStatus == 0){
        ElMessage.error(mainData.value.actPt.errMsg && mainData.value.actPt.errMsg != '数量必须大于0' ? mainData.value.actPt.errMsg : `您还没有加购${mainData.value.showName}哦，请先加购再下单`);
        return false;
    }else if(subsidiaryData.value.actPt.selectStatus == 0){
        ElMessage.error(subsidiaryData.value.actPt.errMsg && subsidiaryData.value.actPt.errMsg != '数量必须大于0' ? subsidiaryData.value.actPt.errMsg : `您还没有加购${subsidiaryData.value.showName}哦，请先加购再下单`);
        return false;
    }
    let bizProducts = JSON.stringify(
        [{
            skuId:mainData.value.id,
            quantity:mianCount.value,
            selectStatus:1,
        },
        {
            skuId:subsidiaryData.value.id,
            quantity:subsidiaryCount.value,
            selectStatus:1,
        }]
    )
    let param = {
    // storeStatus:true,
        bizProducts:bizProducts,
    }
    if (submitDisable.value) {
        return;
    }
    submitDisable.value = true;
    gotoSettle(param).then(res=>{
        if (res.status === 'success') {
            const url = `/merchant/center/order/settle.htm?bizProducts=${encodeURIComponent(
                bizProducts
            )}&storeStatus=true&useRedPacket=true`;
            window.parent.location.href = url;
            submitDisable
        } else {
            ElMessage.error(res.errorMsg)
            submitDisable.value = false;
        }
    })
  }
  const changeProduct = (index)=>{
    try{
        // subsidiaryData.value.actPt.selectStatus = 1;
        // if(subsidiaryData.value.index == subsidiaryDataList.value.length){
        //     subsidiaryData.value = subsidiaryDataList.value[0];
        // }else{
        //     subsidiaryData.value = subsidiaryDataList.value[subsidiaryData.value.index];
        // }
        // subsidiaryCount.value = (subsidiaryData.value || {}).cartProductNum ? (subsidiaryData.value || {}).cartProductNum : subsidiaryData.value.actPt.skuStartNum;
        subsidiaryData.value = subsidiaryDataList.value[index];
        subsidiaryCount.value = subsidiaryDataList.value[index].count;
        loading.value = true;
        changeCart({
            skuId:  subsidiaryData.value.id,
            quantity: subsidiaryCount.value,
            merchantId: proxy.$route.query.merchantId,
        }).then((res) => {
            if (res.status === 'success') {
                if(res.data.qty || res.data.qty === 0){
                    subsidiaryCount.value = res.data.qty === 0 ? subsidiaryData.value.actPt.skuStartNum : res.data.qty;
                    subsidiaryDataList.value[index].count = subsidiaryCount.value
                    if(res.data.qty === 0){
                        subsidiaryData.value.actPt.selectStatus = 0;
                    }
                }
                if(res.data.message || res.data.actPurchaseTip){
                    ElMessage.error(res.data.message ? res.data.message : res.data.actPurchaseTip)
                    if(!res.data.qty && res.data.qty != 0){
                        subsidiaryCount.value = subsidiaryData.value.actPt.skuStartNum;
                    }
                }
                changePrice();
            } else {
                subsidiaryData.value.actPt.errMsg  = res.errorMsg; 
                subsidiaryCount.value = subsidiaryData.value.actPt.skuStartNum; 
                subsidiaryDataList.value[index].count = subsidiaryCount.value
                // subsidiaryData.value.actPt.selectStatus  = 0;
                loading.value = false;
                ElMessage.error(res.errorMsg)
                changePrice();
            }
        })
    }catch(e){
        console.log(e);
    }
  }
  const inputCart = (e,flag) => {
    let num = parseInt(e.target.value);
    num = isNaN(num) || num < 0 ? 0 : num;
    if(flag === 1){
        // mianCount.value = num;
        num =  mainData.value.actPt.skuStartNum > num ? mainData.value.actPt.skuStartNum : num;
    }else{
        // subsidiaryCount.value = num;
        num =  subsidiaryData.value.actPt.skuStartNum > num ? subsidiaryData.value.actPt.skuStartNum : num;
    }
    loading.value = true;
    changeCart({
      skuId: flag === 1 ? mainData.value.id : subsidiaryData.value.id,
      quantity: num,
      merchantId: proxy.$route.query.merchantId,
    }).then((res) => {
      if (res.status === 'success') {
        if(res.data.qty || res.data.qty === 0){
            if(flag === 1){
                mianCount.value  = res.data.qty === 0 ? mainData.value.actPt.skuStartNum : res.data.qty;
                if(res.data.qty === 0){
                    mainData.value.actPt.selectStatus = 0;
                }else{
                    mainData.value.actPt.selectStatus = 1;
                }
              }else{
                subsidiaryCount.value = res.data.qty === 0 ? subsidiaryData.value.actPt.skuStartNum : res.data.qty;
                if(res.data.qty === 0){
                    subsidiaryData.value.actPt.selectStatus = 0;
                }else{
                    subsidiaryData.value.actPt.selectStatus = 1;
                }
              }
          }
        if(res.data.message || res.data.actPurchaseTip){
            ElMessage.error(res.data.message ? res.data.message : res.data.actPurchaseTip)
            if(!res.data.qty && res.data.qty != 0){
                if(flag === 1){
                    mianCount.value = mainData.value.actPt.skuStartNum;
                }else{
                    subsidiaryCount.value = subsidiaryData.value.actPt.skuStartNum;
                }
            }
        }
        changePrice();
      } else {
        if(flag === 1){
            // mainData.value.actPt.selectStatus = 0; 
            mianCount.value = mainData.value.actPt.skuStartNum; 
            mainData.value.actPt.errMsg  = res.errorMsg; 
        }else{
            // subsidiaryData.value.actPt.selectStatus = 1; 
            subsidiaryCount.value = subsidiaryData.value.actPt.skuStartNum; 
            subsidiaryData.value.actPt.errMsg  = res.errorMsg; 
        }
        loading.value = false;
        ElMessage.error(res.errorMsg)
        changePrice();
      }
    })
  }
  const addProductCart = (type,flag) => {
    loading.value = true;
    let num = 0;
    if (type === "add") {
        if(flag === 1){
           num =  ((mianCount.value  +  med_num.value) <= mainData.value.actPt.skuStartNum) ? mainData.value.actPt.skuStartNum : mianCount.value  +  med_num.value;
        }else{
            num =  ((subsidiaryCount.value  + subsidiaryData.value.mediumPackageNum) <= subsidiaryData.value.actPt.skuStartNum) ? subsidiaryData.value.actPt.skuStartNum : subsidiaryCount.value  + subsidiaryData.value.mediumPackageNum;
        }
    } else if (type === "min") {
        if(flag === 1){
            if(mianCount.value  <= 0){
                ElMessage.error('购买数量不能小于0！');
                loading.value = false;
                return false;
            }
            if (mianCount.value > 0) {
                if(mianCount.value <= mainData.value.actPt.skuStartNum) {
                    ElMessage.error('购买数量不能小于起拼数量！');
                    loading.value = false;
                    return;
                }
                if(is_split.value == 1){
                    num = mianCount.value - 1;
                }else{
                    num = mianCount.value - med_num.value;
                }
            }
        }else{
            if(subsidiaryCount.value  <= 0){
                ElMessage.error('购买数量不能小于0！');
                loading.value = false;
                return false;
            }
            if (subsidiaryCount.value > 0) {
                if(subsidiaryCount.value <= subsidiaryData.value.actPt.skuStartNum) {
                    ElMessage.error('购买数量不能小于起拼数量！');
                    loading.value = false;
                    return;
                }
                if(subsidiaryData.value.isSplit == 1){
                    num = subsidiaryCount.value - 1;
                }else{
                    num = subsidiaryCount.value - subsidiaryData.value.mediumPackageNum;
                }
            }
        }
    }
    changeCart({
      skuId: flag === 1 ? mainData.value.id : subsidiaryData.value.id,
      quantity: num,
      merchantId: proxy.$route.query.merchantId,
    }).then((res) => {
        loading.value = false;
      if (res.status === 'success') {
        if(res.data.qty || res.data.qty === 0){
            if(flag === 1){
                mianCount.value  = res.data.qty === 0 ? mainData.value.actPt.skuStartNum : res.data.qty;
                if(res.data.qty === 0){
                    mainData.value.actPt.selectStatus = 0;
                }else{
                    mainData.value.actPt.selectStatus = 1;
                }
              }else{
                subsidiaryCount.value = res.data.qty === 0 ? subsidiaryData.value.actPt.skuStartNum : res.data.qty;
                if(res.data.qty === 0){
                    subsidiaryData.value.actPt.selectStatus = 0;
                }else{
                    subsidiaryData.value.actPt.selectStatus = 1;
                }
              }
          }
        if(res.data.message || res.data.actPurchaseTip){
            ElMessage.error(res.data.message ? res.data.message : res.data.actPurchaseTip)
          }
        changePrice();
      } else {
        if(flag === 1){
            // mainData.value.actPt.selectStatus  = 0; 
            mainData.value.actPt.errMsg  = res.errorMsg; 
        }else{ 
            // subsidiaryData.value.actPt.selectStatus  = 0; 
            subsidiaryData.value.actPt.errMsg  = res.errorMsg; 
        }
        ElMessage.error(res.errorMsg)
      }
    })
  }
  const changePrice = (flag)=>{
    let param = {
        merchantId:proxy.$route.query.merchantId,
        combinedList:JSON.stringify([
            {
                skuId:mainData.value.id,
                qty:mianCount.value === 0 ? mainData.value.actPt.skuStartNum : mianCount.value, 
                isMainProduct:1,
                selectStatus:mainData.value.actPt.selectStatus,
            },
            {
                skuId:subsidiaryData.value.id,
                qty:subsidiaryCount.value === 0 ? subsidiaryData.value.actPt.skuStartNum : subsidiaryCount.value, 
                isMainProduct:0,
                selectStatus:subsidiaryData.value.actPt.selectStatus,
            },
        ])
    }
    loading.value = true;
    combinedInfoQuery(param).then((res) => {
        if (res.status === 'success') {
            combinedDiscountSub.value = res.data.combinedDiscountSub;
            realPay.value = res.data.realPay;
            res.data.combinedList.forEach((it) => {
                if (it.isMainProduct) {
                    mainData.value.actPt.amount = it.costPrice ? it.costPrice  : it.price;
                    mainData.value.actPt.selectStatus = it.selectStatus;
                    mianCount.value = it.qty
                    if(!it.selectStatus){
                        mianCount.value = 0
                        showFlushed.value = false;
                        if(res.data.errMsg){
                            mainData.value.actPt.errMsg = res.data.errMsg;
                        }
                    }else{
                        showFlushed.value = true;
                    }
                } else {
                    discount.value = it.discount;
                    subsidiaryData.value.actPt.amount = it.costPrice ? it.costPrice : it.price;
                    subsidiaryData.value.actPt.selectStatus = it.selectStatus;
                    subsidiaryCount.value = it.qty
                    changeSubProduct(it.skuId,it.qty)
                    if(!it.selectStatus){
                        subsidiaryCount.value = 0;
                        changeSubProduct(it.skuId,0)
                        if(res.data.errMsg){
                            subsidiaryData.value.actPt.errMsg = res.data.errMsg;
                        }
                    }
                }
            });
            nextTick(() => {
                loading.value = false;
                if(res.data.errMsg && flag != 1){
                    ElMessage.error(res.data.errMsg)
                }
            });
        }else {
            ElMessage.error(res.errMsg)
            subsidiaryCount.value = 0
            changeSubProduct(subsidiaryData.value.id,0)
            mianCount.value = 0
            subsidiaryData.value.actPt.amount = subsidiaryData.limitFullDiscount ? subsidiaryData.limitFullDiscount : subsidiaryData?.actPt?.assemblePrice
            mainData.value.actPt.amount = mainData.limitFullDiscount ? mainData.limitFullDiscount : mainData.actPt.assemblePrice
            nextTick(() => {
                loading.value = false;
            });
        }
    })
  }
  onMounted(() => {
    if(proxy.$route.query.flag){    
        init();
    }
  })
  const carouselTime = ref(5000)
  const init = () => {
    loading.value = true;
    searchRecPurchase({
        mainCsuId: proxy.$route.query.id,
        showRecPurchaseType: 1,
    }).then((res) => {
        if (res.status === 'success') {
            loading.value = false;
            scmE.value = res.data.scmId;
            if(res.data.cardInfo){
                title.value = res.data.cardInfo.groupPurchaseInfo.title || '组合购更优惠';
                mainData.value = res.data.cardInfo.groupPurchaseInfo.mainProduct;
                mainData.value.actPt.startAmount = 0
                mainData.value.actPt.amount = 0
                mainData.value.actPt.selectStatus = 1
                mianCount.value = (res.data.cardInfo.groupPurchaseInfo.mainProduct || {}).cartProductNum ? (res.data.cardInfo.groupPurchaseInfo.mainProduct || {}).cartProductNum : res.data.cardInfo.groupPurchaseInfo.mainProduct.actPt.skuStartNum;
                med_num.value = parseInt((res.data.cardInfo.groupPurchaseInfo.mainProduct || {}).mediumPackageNum);
                is_split.value = (res.data.cardInfo.groupPurchaseInfo.mainProduct || {}).isSplit;
                subsidiaryDataList.value = res.data.cardInfo.groupPurchaseInfo.subProducts;
                subsidiaryDataList.value.forEach((item,index)=>{ 
                    item.actPt.selectStatus = 1
                    item.actPt.startAmount = 0
                    item.actPt.amount = 0
                    item.index = index + 1;
                    item.count = item.cartProductNum ? item.cartProductNum : item.actPt.skuStartNum;
                })
                subsidiaryData.value = subsidiaryDataList.value[0];
                fristSubsidaryDataSkuId.value = (subsidiaryData.value || {}).id;
                subsidiaryCount.value = (subsidiaryData.value || {}).cartProductNum ? (subsidiaryData.value || {}).cartProductNum : subsidiaryData.value.actPt.skuStartNum;
                carouselTime.value = (res.data.cardInfo.groupPurchaseInfo.carouselTime || 5) * 1000;
                changePrice(1);
            }else{
                window.parent.document.querySelector('#combination').style.display = 'none'
            }
        } else {
            ElMessage.error(res.errorMsg)
        }
    })
  }
  const carouselChange = (index) => {
    currentIndex.value = index;
    changeProduct(index)
    exposedProducts.clear();
  }
  const carouselRef = ref(null);
  const autoplay = ref(true);
  const restartAutoplay = () => {
    autoplay.value = false; // 先关闭
    setTimeout(() => {
        autoplay.value = true; // 再重新打开（触发重新轮播）
    }, 100);
  }
  const setActiveItem = (index) => {
    if (carouselRef.value) {
      carouselRef.value.setActiveItem(index);
      currentIndex.value = index;
      restartAutoplay()
    }
  }
  const prevSlide = () => {
    if (carouselRef.value) {
        carouselRef.value.prev();
        restartAutoplay()
    }
   };

  const nextSlide = () => {
    if (carouselRef.value) {
        carouselRef.value.next();
        restartAutoplay()
    }
  };
  const changeSubProduct = (skuId,qty) => { 
    subsidiaryDataList.value.forEach(item => {
        if(item.id == skuId) {
            item.count = qty
        }
    })
  }
  onUnmounted(() => {
    
  });
  </script>
  <style lang="scss" scoped>
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(44, 43, 43, 0);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    color: #161616;
    z-index: 999;
  }
  .pricewapper {
        .price-box {
            overflow: hidden;
            color: #FF2121;
            .price-two {
                p {
                    color: #FF2121;
                }
                letter-spacing: 0;
                span {
                    font-weight: bold;
                }
                .priceDec {
                    font-size: 14px;
                }
                .priceInt {
                    font-size: 22px;
                }
                .priceFloat {
                    font-size: 18px;
                }
            }
        }
    }
  .wrapperBox {
    width: 1200px;
    background: #fafafa;
    margin: 0 auto;
    padding-bottom: 30px;
    // padding-top: 10px;
    font-size: 12px;
    height: 230px;
    background-image: linear-gradient(179deg, #FF3131 0%, #FFE9E9 79%);;
    border: 1px solid #FFB9B0;
    border-radius: 5px;
    .top{
        display: flex;
        height: 40px;
        width: auto;
        line-height: 40px;
        padding-left: 20px;
        background-image: url('../../../assets/images/search/combination-header.png');
        background-repeat: no-repeat;
        background-size: cover; /* 填满元素 */
        background-position: center; /* 居中显示 */
        background-position: 0 0;
        .middle { 
            display: flex;
            align-items: center;
        }
        .fox{
            margin-left: 8px;
            font-weight: 500;
            font-size: 18px;
            color: #FFFFFF;
        }
    }
    .main {
        position: relative;
        display: flex;
        background: #FFFFFF;
        width: 98%;
        margin: 0 auto;
        border-radius: 10px;
        .middle {
            height: 200px;
            user-select: none;
            margin: 0 30px 0 30px;
            img {
                width: 24px;
                height: 24px;
                margin-top: 83.5px;
            }
        }
        .custom-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 29px;
            height: 55px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            z-index: 10;    
            i {
                font-size: 20px;
            }
        }
        .custom-arrow--left {
            background-image: url('../../../assets/images/search/targetLeft.png');
            background-repeat: no-repeat;
            background-size: cover; /* 填满元素 */
            background-position: center; /* 居中显示 */
            left: -1%;
        }
        .custom-arrow--right {
            background-image: url('../../../assets/images/search/targetRight.png');
            background-repeat: no-repeat;
            background-size: cover; /* 填满元素 */
            background-position: center; /* 居中显示 */
            right: -1%;
        }
        .el-carousel {
            width: 90%;
            padding: 0 !important;
            .carousel-content {
                display: flex;
                width: 100%;
                height: 100%;
                align-items: center;
                justify-content: space-between;
            }
        }
        .combinationRight {
            width: 25%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: start;
        }
        .custom-indicators {
            position: absolute;
            bottom: 18px;
            right: 28px;
            display: flex;
            gap: 4px;
            z-index: 10;
            
            span {
                border-radius: 50%;
                width: 6px;
                height: 6px;
                background: #DFDFDF;
                cursor: pointer;
                transition: background-color 0.3s;
                
                &.active {
                    background: #666666;
                }
            }
        }
    }
    .combinationLeft{
        height: 156px;
        width: 382px;
        display: flex;
        margin-top: 30px;
        .images {
          margin-left: 20px;
          width: 160px;
          height: 159px;
          position: relative;
          .pic {
            max-width: 160px;
            max-height: 160px;
            display: block;
            margin: auto;
            object-fit:contain;
            }
        }
    }
    .combinationCenter{
        height: 160px;
        width: 382px;
        display: flex;
        margin-top: 30px;
        .images {
        //   margin-left: 20px;
          width: 160px;
          height: 160px;
          position: relative;
          .pic {
            max-width: 160px;
            max-height: 160px;
            display: block;
            margin: auto;
            object-fit:contain;
            }
            .activity-token-combination {
            position: absolute;
            height: 26px;
            font-size: 14px;
            background: #FF6204;
            border-radius: 4px 0 0 4px;
            // max-width: 100%;
            margin-top: -2px;
            top: 2px;
            right: 0px;
            z-index: 3;
            div{
              margin: 5px 5px 0 5px;
              text-align: center;
              color: #FFFFFF;
              height: 14px;
              letter-spacing: 0;
              line-height: 14px;
              font-weight: 500;
            }
          }
        }
    }
    .combinationRight{
        height: 130px;
        width: 240px;
        margin-top: 30px;
        margin-left: 50px;
        .preferential{
            display: flex;
            justify-content: space-between;
            width: 208px;
            margin-top: 16px;
            .item{
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                letter-spacing: 0;
                line-height: 14px;
                font-family: PingFangSC-Regular;
            }
        }
        .arrivalPrice{
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 208px;
            margin: 16px 0;
            .item{
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                letter-spacing: 0;
                line-height: 14px;
                font-family: PingFangSC-Regular;
            }
        }
        .combinationBuy {
            z-index: 9;
            cursor: pointer;
            width: 208px;
            height: 40px;
            background: #FD1C2F;
            border-radius: 5px;
            font-size: 16px;
            font-family: MicrosoftYaHei;
            text-align: center;
            color: #ffffff;
            line-height: 42px;
            &.disabled {
                background: #b3e5cd !important; /* 禁用时的背景颜色 */
                color: #e0e0e0 !important; /* 禁用时的文字颜色 */
                cursor: not-allowed !important; /* 禁用时的鼠标样式 */
            }
        }
    }
    .leftRight{
        cursor: pointer;
        width: 206px;
        margin-left: 14px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .price-mian {
            height: 35px;
            width: auto;
            line-height: 35px;
            margin-top: 3px;
            background-image: url("./../../../assets/images/search/mianPriceBJ.png");
            background-repeat: no-repeat;
            background-size: cover; /* 填满元素 */
            background-position: center; /* 居中显示 */
            border-radius: 1px;
        }
        .price-sub {
            height: 35px;
            width: auto;
            line-height: 35px;
            margin-top: 3px;
            background-image: url("./../../../assets/images/search/subPriceBJ.png");
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
            border-radius: 1px;
            display: flex;
            position: relative;
            
            p {
                flex: 1;
                margin: 0;
                padding-left: 10px;
                color: #FF2121;
            }
            
            .price-dec {
                position: absolute;
                right: 0;
                top: 0;
                width: 70px;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                color: #FFFFFF;
                font-size: 12px;
                line-height: 1.2;
                padding-right: 5px;
                font-weight: 400;
                
                .title {
                    display: block;
                    margin-bottom: 1px;
                    font-weight: 400;
                }
            }
        }
        .showName{
            padding: 2px 0;
            overflow: hidden;
            .commonName {
                // height: 36px;
                font-size: 16px;
                font-family: PingFangSC, PingFangSC-Regular;
                font-weight: 400;
                text-align: left;
                color: #333333;
                line-height: 16px;
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                /* autoprefixer: off */
                -webkit-box-orient: vertical;
                /* autoprefixer: on */
                -webkit-line-clamp: 2;
            }
        }
        .manufacturer {
            padding: 2px 0;
            font-family: PingFangSC-Regular;
            background: none;
            font-size: 14px;
            color: #666666;
            letter-spacing: 0;
            text-align: justify;
            line-height: 22px;
            font-weight: 400;
          }
        .nearEffectBox {
            padding: 2px 0;
            position: relative;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            letter-spacing: 0;
            text-align: justify;
            line-height: 15px;
        }
        .content {
            margin: 5px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .spread-add {
            display: flex;
            display: -webkit-flex;
            flex-wrap: nowrap;
            -webkit-flex-wrap: nowrap;
            box-sizing: border-box;
            z-index: 9;
            border-radius: 6px;
            border: 1px solid #d7d7d7;
            overflow: hidden;
            .plus,
            .reduce {
                width: 26px;
                height: 30px;
                line-height: 30px;
                color: #757575;
                text-align: center;
                background: #EFEFEF;
                cursor: pointer;
                font-size: 20px;
            }
            .plus {
                border-left: 1px solid #d7d7d7;
            }
            .reduce {
                border-right: 1px solid #d7d7d7;
            }
            .cont {
                width: 44px;
                .input-val {
                box-sizing: border-box;
                padding: 0;
                width: 100%;
                text-align: center;
                color: #292933;
                border: none;
                outline: none;
                font-size: 16px;
                font-family: MicrosoftYaHei;
                text-align: left;
                color: #333333;
                line-height: 30px;
                text-align: center;
                }
            }
            }
        }
    }
  }
  ::v-deep .el-carousel--horizontal, .el-carousel--vertical  {
    position: relative;
  }
  ::v-deep .el-carousel__arrow {
        display: none !important;
    }
  ::v-deep .el-carousel__indicators--horizontal.el-carousel__indicators--outside {
    position: absolute;
    top: 26%;
    width: 100px;
    height: 30px;
    left: 84%;
  }
  ::v-deep .el-carousel__indicators--horizontal.el-carousel__indicators--outside button {
    border-radius: 50%;
    width: 5px;
    height: 5px;
  }
  </style>
  
