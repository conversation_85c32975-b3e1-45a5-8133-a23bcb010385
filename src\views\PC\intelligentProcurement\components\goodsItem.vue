<template>
  <div class="goodsInfo" :style="[ { 'background-color': backgroundColor } ]">
	<div  class="goodsBasicInfo" v-if="!excelInfo.noMatchFlag&&excelInfo.skus?.length||showSearchVis">
			<el-checkbox :checked="Boolean(info.selectStatus)" @change="changeCheckbox" />
			<div class="imgBox" @click="toDetailPage(info.skuId)">
				<img v-if="showMatchIcon" class="matchIcon" src="../../../../assets/images/intelligentProcurement/goodsIcon.png">
				<img
					class="goodsImg"
					v-lazy="imgUrl + '/ybm/product/min/' + info.imageUrl"
          			:key="info.imageUrl"
					:alt="info.showName"
				/>
			</div>
			
			<div class="infoBox"  @click="toDetailPage(info.skuId)">
				<div><span class="name">{{ info.showName }}</span></div>

				<div><span :style="{color:(info.spec==excelInfo.excelSpec||excelInfo.excelSpec==''?'black':'red'),margin:('8px 0 0')}">{{ info.spec }}</span></div>

				<div :style="{color:(info.manufacturer==excelInfo.excelManufacturer||excelInfo.excelManufacturer==''?'black':'red'),margin:('8px 0 0')}">{{ info.manufacturer }}</div>

				<div v-if="info.approvalNumber" :style="{color:(info.approvalNumber==excelInfo.excelApprovalNumber||excelInfo.excelApprovalNumber==''?'black':'red'),margin:('8px 0 0')}">{{ info.approvalNumber }}</div>

				<div :style="{color:(info.code==excelInfo.excelCode||excelInfo.excelCode==''?'black':'red')}">{{ info.code }}</div>

				<!-- <div class="shopBox" @click="toShopPage(info.pcShopUrl)">
					<span class="commonTag zyTag" v-if="info.isThirdCompany === 0">自营</span>
					<span class="commonTag pzTag" v-else-if="info.isThirdCompany === 1 && info.isQualityShop === 1">品质</span>
					<span class="shopName">{{ info.shopName }} </span>&nbsp;<el-icon style="vertical-align: middle;"><ArrowRight /></el-icon>
					
				</div> -->
			</div>
		</div>
		
		<div style="width:310px;margin-right:30px;display: flex;align-items: center;" v-else-if="curtab==2">未匹配到您想要的商品，可点击“搜索”查询您想要的商品</div>
		<div style="width:310px;margin-right:30px;display: flex;align-items: center;" v-else>{{ excelInfo.noMatchMsg }}</div>
		
		<div class="importInfoBox" v-if="!showSearchVis">
			<div><span class="lineNum" v-if="excelInfo?.lineNum">{{excelInfo.lineNum}}</span>{{ excelInfo.excelCommonName?excelInfo.excelCommonName:'-'  }} </div>
			<div style="margin-top:8px">{{ excelInfo.excelSpec?excelInfo.excelSpec:'-' }}</div>
			<div style="margin-top:8px">{{ excelInfo.excelManufacturer?excelInfo.excelManufacturer:'-' }}</div>
			<div style="margin-top:8px;">{{excelInfo.excelApprovalNumber?excelInfo.excelApprovalNumber:'-'}}</div>
			<div style="margin-top:8px;">{{ excelInfo.excelCode?excelInfo.excelCode:'-' }}</div>
		</div>
		<div class="countBox">
			<div  v-if="!curtab||curtab==1||excelInfo.skus.length">
				<div>
				<AddCartStatic :goodsInfo="info" :excelInfo="excelInfo" :hideSubTotal="hideSubTotal" />
			</div>
			<div style="margin-top:8px;">
				<span style="vertical-align: middle;">导入数量：{{ excelInfo.excelBuyNum }}</span>
			
				<el-icon  style="vertical-align: middle;cursor: pointer;" @click="showEdit" v-if="!showSearchVis">
      				<Edit />
    			</el-icon>
			</div>
			<div v-if="info.leastPurchaseNum||info.limitedQty"> <span  v-if="info.leastPurchaseNum" style="margin-right:10px;">{{ info.leastPurchaseNum }}{{ info.productUnit }}起购</span> 	<span  v-if="info.limitedQty">限购{{ info.limitedQty }}{{ info.productUnit }}</span>&nbsp;</div>	
			<div style="margin-top:8px;">
				库存：{{ info.availableQty>100?'有货':info.availableQty }}
			</div>
			
			<div style="margin-top: 8px;">中包装：{{ info.mediumPackageNum }}{{ info.productUnit }} <span v-if="info.isSplit === 0">（不可拆零）</span></div>
			</div>
		
		</div>
		<div class="priceBox" >
			<div v-if="!curtab||curtab==1||excelInfo.skus.length">
			<div>
				<i class="fobText">￥{{  (info.discountPrice || 0).toFixed(2) }}</i>
			</div>
			<!-- Number(info.purchasePrice.split('￥')[1]) -->
			<span class="zheHouPrice" v-if="info.purchasePrice && info.purchasePrice <= info.discountPrice" @click="showZheHouVis(info)">
				折后约<p style="display: inline;font-weight: 600;font-size: 16px;margin:0 2px;">￥ {{ info.purchasePrice }}</p>
				<el-tooltip placement="top" v-if="info.levelDiscountInfos?.length">
					<template #content>
						<p v-for="(discountInfo, index) in info.levelDiscountInfos" :key="index">
							满 {{ discountInfo.startQty }} {{ info.productUnit }}, {{ discountInfo.discountRate }} 元/{{ info.productUnit }}
						</p> 
					</template>
					<el-icon style="cursor: pointer;"><Warning /></el-icon>
				</el-tooltip>
			</span>
			<div class="lifeTime" style="margin:10px 0">效期近至：{{  info.nearEffect    }}</div>
			<div class="lifeTime">效期远至：{{  info.farEffect    }}</div>

			<div class="tagBox">
				<span class="tagItem" v-for="item in info.tagList" :key="item" :class="`span${item.uiType}`">{{ item.name }}</span>
				<span class="tagItem span5" v-if="info.orderedUnClaimed && info.orderedUnClaimed.length > 0" style="cursor: pointer;" @click="couponDialog()">领取优惠券</span>
			</div>	
			</div>
			
		</div>
		<div class="merchantBox">
			<div class="shopBox" @click="toShopPage(info.pcShopUrl)">
					<span class="commonTag zyTag" v-if="info.isThirdCompany === 0">自营</span>
					<span class="commonTag pzTag" v-else-if="info.isThirdCompany === 1 && info.isQualityShop === 1">品质</span>
					<span class="shopName">{{ info.shopName }} </span>&nbsp;<el-icon v-if="info.shopName" style="vertical-align: middle;"><ArrowRight /></el-icon>
					
				</div>
			<div v-for="(item,index) in info.shopLabels" :key="index" class="merchantLabel"><span v-if="item!=='自营'">{{item}}</span></div>
		</div>
		<div class="operatorBox" >
			<span @click="showSearch(excelInfo.noMatchFlag)" v-if="!showSearchVis" :class="{'searchBtn':excelInfo.noMatchFlag==14}" >  
			<el-icon style="vertical-align: middle"><Search /> </el-icon>
			<span style="vertical-align: middle" >搜索</span> 
			</span> 
			<span v-else @click="addBuyPlan" style="color:rgb(0, 185, 85);">加入采购计划</span></div>
		<!-- <div v-if="!hideSubTotal" class="">小计 <span class="subTotal">￥{{ (info.subTotal || 0).toFixed(2) }}</span></div> -->
		<el-dialog v-model="couponsData.status" width="800px" title="领取优惠券" @close="couponsDialogClose">
			<div class="coupons-box">
				<div v-for="(item, i) in couponsData.form" :key="i" class="coupons-item">
					<div>
						<div>
							<span style="font-size: 16px;">￥</span>
							<span style="font-size: 30px;">{{ item.moneyInVoucher }}</span>
						</div>
						<span style="font-size: 12px;">{{ item.minMoneyToEnableDesc }}</span>
					</div>
					<div>
						<p>
							<span class="tag">{{ item.voucherTypeDesc }}</span>
							<span>{{ item.shopName }}</span>
						</p>
						<p style="font-size: 12px;margin-top: 10px;color:#7c7c7c;">
							{{ item.voucherTitle }}
						</p>
						<p class="btn-box">
							<span style="font-size: 12px;color: #aaaaaa;">{{ item.voucherScope }}</span>
							<div v-if="item.state == 1" class="btn" @click="getCoupon(item)">立即领取</div>
							<div v-if="item.state == 2" class="btn" style="background: linear-gradient(90deg, rgb(255 180 153) 0%, rgb(255 129 130) 100%);cursor: default;">已领取</div>
						</p>
						<p style="font-size: 12px;color: #aaaaaa;" v-if="item.validDate && item.expireDate">{{ `${(new Date(item.validDate)).toLocaleDateString('zh').replaceAll('/','.')} -- ${(new Date(item.expireDate)).toLocaleDateString('zh').replaceAll('/','.')}` }}</p>
					</div>
				</div>  
			</div>
		</el-dialog>
		<el-dialog v-model="showZheHouPriceVis" width="500px" title="折后价" @close="cancelZheHouVis">
			<div class="zhehou-box">
				<div class="font-title">
					商品折后价为叠加所有可用满折、满减活动后的最优购买单价。实际购买单价以结算页为准。
				</div>
				<div class="zhehou-price-content">
					<div class="zhehou-price-item">
						<div class="zhehou-price-title">商品采购价</div>
						<div class="zhehou-price-value">￥{{  (info.discountPrice || 0).toFixed(2) }}</div>
					</div>
					<div class="zhehou-price-item">
						<div class="zhehou-price-title">满折优惠满2000元打9折</div>
						<div class="zhehou-price-value">￥100</div>
					</div>
					<div class="zhehou-price-item">
						<div class="zhehou-price-title">折后价</div>
						<div class="zhehou-price-value red">{{ info.purchasePrice }}</div>
					</div>
				</div>
				<div class="price-btn">
					<el-button type="primary" @click="cancelZheHouVis">知道了</el-button>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import { ref, inject, computed } from 'vue'
import { receiveCoupon,satisfactoryInHandPriceWithDetail } from '../../../../http_pc/api';
import AddCartStatic from '../../components/addCartStatic.vue';
import { ElMessage  } from 'element-plus';
import { getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
	showSearchVis:{
		default:''
	},
	goodsInfo: {
		default: {}
	},
	excelInfo: {
		default: {}
	},
	// 是否隐藏“小计”一栏
	hideSubTotal: {
		default: false
	},
	// 是否显示“已匹配”icon
	showMatchIcon: {
		default: false
	},
	excelCommonName:{
		default:''
	},   
	excelSpec:{
		default:''
	},
	excelManufacturer:{
		default:''
	},
	lineNum:{
		default:''
	},
	curtab:{
		default:''
	}
});
	const imgUrl = import.meta.env.VITE_IMG;
	const info = ref(props.goodsInfo);
	const couponsData = ref({
		status: false,
		form: {},
		loading: false
	})
	const showZheHouPriceVis = ref(false);
/* 	const orderedUnClaimed = {};
	orderedUnClaimed.templateId = 95388;
	orderedUnClaimed.templateName = "点击阿萨德囧";
	orderedUnClaimed.voucherType = 1;
	orderedUnClaimed.voucherTypeDesc = "通用券";
	orderedUnClaimed.state = 1;
	orderedUnClaimed.voucherTitle = 'hiu阿时候对啊谁都会';
	orderedUnClaimed.moneyInVoucher = 12;
	orderedUnClaimed.minMoneyToEnableDesc = '满100可用';
	orderedUnClaimed.discount = 123;
	orderedUnClaimed.validDate = Date.now();
	orderedUnClaimed.expireDate = Date.now() + 123123123;
	orderedUnClaimed.isAvailable = true;
	orderedUnClaimed.shopName = '阿凯一打';
	orderedUnClaimed.voucherScope = '部分商品可用';
	info.value.orderedUnClaimed.push(orderedUnClaimed);
	info.value.orderedUnClaimed.push(orderedUnClaimed); */
	// 接收爷爷的空的函数
	const sendGrandson = inject('sendHandle');
	const activeName = inject('activeName')
	 const BASEURL = import.meta.env.VITE_BASE_URL_PC;
	const toDetailPage = (id)=>{
	// 	window.parent.postMessage(
    //   {
    //     action: "removeIframe",
    //   },
    //   `${window.location.origin}/search/skuDetail/${id}.htm`
    //   // `https:${import.meta.env.VITE_BASE_URL_PC}search/skuDetail/${route.query.id}.htm`
    //   // `https:${"//new-www.test.ybm100.com/"}search/skuDetail/62565142.htm`
    // ); // 通知父网页移除iframe
	window.open(`${BASEURL}search/skuDetail/${id}.htm`);
	}
	const backgroundColor = computed(()=>{
		if (info.value && info.value.nearEffectiveFlag!==0) {
        return '#FFF7F7';
    	}
    	return '#ffffff';
	})
	const emit = defineEmits(['changeIsCHangeStatus','showSearch','addBuyPlan','showEditCount']);
	const changeCheckbox = (e) => {
		emit('changeIsCHangeStatus',info.value.skuId)
		info.value.selectStatus = Number(e);
		// 极速搜索引用的此组件不需向上通信
		if (!props.hideSubTotal) {
			// 触发函数，向爷爷通信
			sendGrandson(info.value.skuId,e)
		}
	}
	const addBuyPlan = ()=>{
		emit('addBuyPlan',props.goodsInfo)
	}

	const toShopPage = (url) => {
		window.open(url);
	}
	const couponDialog = () => {
		couponsData.value.status = true;
		couponsData.value.form = info.value.orderedUnClaimed;
		console.log(couponsData.value);
	}
	const getCoupon = (item) => {
		if (couponsData.value.loading) return;
		couponsData.value.loading = true;
		console.log(proxy);
		receiveCoupon({
			merchantId: proxy.$merchantId,
			voucherTemplateId: item.templateId,
		}).then(res => {
			if (res.status === "success") {
				ElMessage.success('领取成功');
				item.state = 2;
			} else {
				ElMessage.error('领取失败');
			}
		}).finally(() => {
			couponsData.value.loading = false;
		})
	}
	const couponsDialogClose = () => {
		couponsData.value.status = false;
		info.value.orderedUnClaimed = couponsData.value.form;
		couponsData.value.form = [];
	}
	const showSearch = (flag)=>{
		if(flag==14)return
		emit('showSearch',props.excelInfo)
	
	}

	const showEdit = ()=>{
		emit('showEditCount',props.excelInfo)
	}
	const showZheHouVis = (detail) => {
		console.log(proxy);
		let params = {
			skuId: detail.skuId,
			merchantId: proxy.$merchantId,
		}
		satisfactoryInHandPriceWithDetail(params).then(res => {
			console.log(res);
		})
		showZheHouPriceVis.value = true;
	}
	const cancelZheHouVis = () => {
		showZheHouPriceVis.value = false;
	}	
</script>
<style lang="scss" scoped>
	.goodsInfo {
		background: #fff;
		padding: 20px;
		border-bottom: 1px solid #E0E0E0;
		display: flex;
		//align-items: center;
		//justify-content: space-between;
		font-family: PingFangSC-Regular;
		position: relative;
	}
	.goodsBasicInfo {
		display: flex;
		width:310px;
		margin-right: 30px;
		align-items: center;
		cursor: pointer;
		.imgBox {
			width: 100px;
			height: 100px;
			margin-left: 18px;
			position: relative;
			.goodsImg {
				position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 100px;
        max-height: 100px;
			}
			.matchIcon {
				position: absolute;
				left: 0;
				top: 0;
				width: 62px;
				height: 62px;
				z-index: 10;
			}
		}
	}
	.infoBox >div {
		margin-top: 6px;
	}
	.infoBox {
		color: #666;
		margin-left: 20px;
		width: 300px;
		.name {
			color: #222222;
			font-size: 16px;
			font-weight: bold;
			margin-right: 6px;
		}
		.shopBox {
			//display: flex;
			align-items: center;
			margin-top: 8px;
			.commonTag {
				padding: 2px 4px;
				font-size: 12px;
				margin-right: 3px;
				border-radius: 2px;
			}
			.zyTag {
				background: #00B377;
				color: #fff;
			}
			.pzTag {
				background: #FEE8BD;
				color: #633808;
			}
			.shopName {
				color: #222222;
				cursor: pointer;
				vertical-align: middle;
			}
			img {
				width: 14px;
				height: 14px;
				margin-left: 6px;
			}
		}
	}
	.priceBox {
		color: #666666;
		//margin-left: 50px;
		margin:0 10px;
		width:140px;
		div {
			margin-top: 4px;
		}
		.fobText {
			color: #FF2122;
			font-size: 16px;
			font-weight: bold;
		}
		.optimalPrice {
			color: #FF2122;
			font-size: 14px;
		}
		.tagBox {
			margin-top: 8px;
			//height: 18px;
			.tagItem {
				border-radius: 2px;
				// padding: 0px 4px;
				margin-right: 6px;
				font-size: 14px;
				height: 18px;
				line-height: 18px
			}
		}
		/*标签样式*/
    @import "../../../../assets/style/tagStyle";
	}
	.countBox {
		width:190px;
		//margin: 0 80px;
		margin:0 10px;
		color: #666666;
		.leastNum {
			color: #FF5B5B;
			margin:8px 0 0;
		}
		.limitText {
			color: #F39800;
		
			font-size: 14px;
			margin-top:8px;
		}
	}
	.subTotal {
		color: #292933;
		font-size: 16px;
		font-weight: bold;
	}
	.coupons-box {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 15px;
		.coupons-item {
			display: flex;
			height: 105px;
			box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.1);
			border-radius: 7px;
			overflow:hidden;
			>div:first-child {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 90px;
				background-color:#fae4e4;
				color: #FF2121;
			}
			>div:last-child {
				padding: 5px;
				flex-grow: 1;
			}
			.tag {
				padding: 2px 5px;
				border-radius: 25px;
				font-size: 12px;
				color:white;
				background: linear-gradient(90deg,rgba(255,213,141,1) 0%,rgba(255,171,84,1) 100%);
				margin-right: 5px;
			}
			.btn-box {
				margin-top: 15px;
				display: flex;
				justify-content: space-between;
				.btn {
					background: linear-gradient(90deg,rgba(255,96,37,1) 0%,rgba(255,66,68,1) 100%);
					color: white;
					padding: 5px 8px;
					border-radius: 25px;
					cursor: pointer;
				}
			}
		}
		
	}
	.importInfoBox{
		width:193px;
		padding-right:27px;
		margin:0 10px;
		color:gray;
	}
	.merchantBox{
		width:110px;
		cursor:pointer
	}
	.operatorBox{
		cursor:pointer;

		position: absolute;
		right:40px;
		top:50%;
		transform: translateY(-50%);
	}
	.lineNum{
		background: #00B377;
		color:white;
		padding:2px 4px;
		display: inline-block;
		margin-right:3px;
		border-radius: 2px;
		font-size: 12px;
	}
	.lifeTime{
		font-size: 12px;
		color:rgb(160, 158, 158)
	}
	.merchantLabel{
		margin-top:8px;
		color:rgb(0, 185, 85);
		margin-right:5px;
	}
	.searchBtn{
		color:gray;
		cursor: not-allowed;
	}
	.zheHouPrice {
		color: #FF2122 !important;
		font-size: 14px !important;
		padding: 0 !important;
		cursor: pointer;
	}
	.zhehou-box {
		.zhehou-price-content {
			padding-top: 5px;
			.zhehou-price-item {
				display: flex;
				justify-content: space-between;
				margin: 5px 0;
				.zhehou-price-title {
					font-weight: 600;
				}
				.zhehou-price-value {
					font-weight: 600;
				}
				.red {
					color: #FF2121;
				}
			}
		}
		.price-btn {
			text-align: center;
		}
	}
</style>