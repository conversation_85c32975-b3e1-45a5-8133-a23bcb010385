<template>
<div>
	<el-dialog
 class="main-body"
    v-model="showRuleVis"
    title="设置匹价规则"
    width="800px"
		:lock-scroll="false"
    :before-close="handleClose"
	top="10vh"
  >
  <div style="height:1px ;background-color: #EFEFEF;width:800px;height:1px;position: relative;right:16px;margin-bottom:16px"></div>
    <p class="tips">根据勾选的匹配规则筛选价格最优的商品，更新比价规则后会对导入的数据重新匹配</p>

	<div class="titleBox">
			<PartTitle title="参与比价的采购条件" />
			<span style="margin-right:auto;">
				<img src="../../../../assets/images/intelligentProcurement/tip.png" class="tipIcon" alt="">
				满足以下条件的商家才可进行匹配，若不满足采购条件则匹配失败，建议谨慎使用  
			</span>
			<span style="color:#999999;cursor:pointer;margin-right:6px;" @click="foldData.one= !foldData.one">{{ foldData.one ? '展开' : '收起' }}
				<el-icon style="margin-left:5px;"><ArrowDown v-show="!foldData.one" /> <ArrowUp v-show="foldData.one"/></el-icon>
			</span>
			
		</div>
		<div :style="{height:`${foldData.one ? '0px' : 'auto'}`}" style="overflow: hidden;">
			<div style="margin: 20px 0 0 10px">
				<el-checkbox-group v-model="ruleArr">
					<el-checkbox :label="1">
						<span class="checkboxText">
							效期大于等于
							<el-input style="width: 60px" :model-value="nearEffectDay" @input="inputValue" :disabled="ruleArr.indexOf(1) === -1" />
							月
						</span>
					</el-checkbox>
				
					<el-checkbox :label="2"><span class="checkboxText">库存满足采购数量</span></el-checkbox>
					<el-checkbox :label="3" style="color:#222222;">限购满足采购数量</el-checkbox>
					<el-checkbox :label="4" style="color:#222222;">起订满足采购数量</el-checkbox>
					<el-checkbox :label="5" style="color:#222222;">追溯码</el-checkbox>
				</el-checkbox-group>
			</div>
		</div>

		<div class="titleBox">
			<PartTitle title="活动类型" />
			
			<span style="margin-left: auto;color:#999999;cursor:pointer;margin-right:6px;" @click="foldData.two= !foldData.two">
				{{ foldData.two ? '展开' : '收起' }}
				<el-icon style="margin-left:5px;"><ArrowDown v-show="!foldData.one" /> <ArrowUp v-show="foldData.one"/></el-icon>
			</span>
		</div>
		<div :style="{height:`${foldData.two ? '0px' : 'auto'}`}" style="overflow: hidden;">
			<div style="margin: 20px 0 0 10px">
				<el-radio-group v-model="promotionType">
					<el-radio :label="0"><span class="checkboxText">全部</span></el-radio>
					<el-radio :label="1" style="color:#222222;">拼团&批购包邮</el-radio>
					<el-radio :label="2" style="color:#222222;">可凑单 <span style="color:#9B9B9B">（不包含拼团&批购包邮活动）</span></el-radio>
				</el-radio-group>
			</div>
		</div>

		<div class="titleBox">
			<PartTitle title="参与比价的商家范围" />
			<span style="margin-right:auto;">
				<img src="../../../../assets/images/intelligentProcurement/tip.png" class="tipIcon" alt="">
				在所选商家内进行匹配最低价商品，建议全选
			</span>
			<span style="color:#999999;cursor:pointer;margin-right:6px;" @click="foldData.three= !foldData.three">{{ foldData.three ? '展开' : '收起' }}
				<el-icon style="margin-left:5px;"><ArrowDown v-show="!foldData.one" /> <ArrowUp v-show="foldData.one"/></el-icon>
			</span>
		</div>
		<div :style="{height:`${foldData.three ? '0px' : 'auto'}`}" style="overflow: hidden;">
			<div class="shopWrapper">
				<div class="shopTitle">
					<el-checkbox
						v-model="checkAll"
						@change="checkAllShopsChange"
					>
						<span  class="checkboxText" >自营全选</span>
					</el-checkbox>
					<div style="color:#9B9B9B">{{ shopCodes.length }}/{{ shopList.length }}</div>
				</div>
				<div class="shopContent">
					<el-checkbox-group
						v-model="shopCodes"
						@change="checkShopChange"
					>
						<el-checkbox v-for="item in shopList" :key="item.shopCode" :label="item.shopCode" class="checkboxItem">
							<span class="checkboxText">{{ item.showName }}</span>
						</el-checkbox>
					</el-checkbox-group>
				</div>
			</div>
			<div class="shopWrapper">
				<div class="shopTitle">
				
					<el-checkbox
			
						v-model="isAllSelectedRecMerchant"
						:indeterminate="isIndeterminatePop"
						@change="checkAllPopShopsChange"
					>
						<span class="checkboxText">平台推荐商家全选</span>
					</el-checkbox>
					<div style="color:#9B9B9B">{{ GOOD_CHAIN_SHOP_CODE.length+HIGH_QUALITY_SHOP_CODE.length+BEFORE_SHOP_CODE.length }} / {{ HIGH_QUALITY_SHOP.length + GOOD_CHAIN_SHOP.length + BEFORE_SHOP.length }}</div>
				</div>
				<div class="shopContent">
					<el-checkbox  v-model="useHistory" @change="toggleShopedChecked">
							<span class="checkboxText">【采购过】</span>&nbsp;
							<span style="color:#00B955;padding:10px 0" @click="lookBEFORE_SHOP">(查看：{{ BEFORE_SHOP_CODE.length }} / {{ BEFORE_SHOP.length }})</span>
					</el-checkbox>
					
					
					<el-checkbox  v-model="useHighQuality"  @change="toggleQualityChecked">
							<span class="checkboxText">全部品质商家</span>&nbsp;
							<span style="color:#00B955;padding:10px 0" @click="lookHIGH_QUALITY_SHOP">(查看：{{ HIGH_QUALITY_SHOP_CODE.length }} / {{ HIGH_QUALITY_SHOP.length }})</span>
					</el-checkbox>
				


					<el-checkbox  v-model="useGoodChain" @change="toggleGoodChecked" >
							<span class="checkboxText">连锁好店商家</span>&nbsp;
							<span style="color:#00B955;padding:10px 0" @click="lookGOOD_CHAIN_SHOP">(查看：{{ GOOD_CHAIN_SHOP_CODE.length }} / {{ GOOD_CHAIN_SHOP.length }})</span>
					</el-checkbox>
				
					<el-checkbox  v-model="useOther" @change="toggleOtherChecked" >
							<span class="checkboxText">其他商家</span>&nbsp;
							<span style="color:#00B955;padding:10px 0" @click="lookOTHER_SHOP">(查看：{{ OTHER_SHOP_CODE.length }} / {{ OTHER_SHOP.length }})</span>
					</el-checkbox>
						
					
					
				</div>
			</div>
		</div>

		
		<!-- <div class="titleBox">
			<PartTitle title="比价标准" />
			<span>
				<img src="../../../../assets/images/intelligentProcurement/tip.png" class="tipIcon" alt="">
				选择按照药帮忙价（商品单价）还是折后价（叠加优惠活动后单价）进行比价  
			</span>
			<span style="margin-left: 10px;color:#222222;cursor:pointer;" @click="foldData.three= !foldData.three">{{ foldData.three ? '展开' : '收起' }}</span>
		</div>
		<div :style="{height:`${foldData.three ? '0px' : 'auto'}`}" style="overflow: hidden;">
			<div style="margin: 20px 0 0 10px">
				<el-radio-group v-model="matchPriceType">
					<el-radio :label="1"><span class="checkboxText">药帮忙价</span></el-radio>
					<el-radio :label="2"><span class="checkboxText">折后价</span><span style="color: #9B9B9B">（叠加优惠活动后的预估价格）</span></el-radio>
				</el-radio-group>
			</div>
		</div> -->
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleOk">保存</el-button>
      </span>
    </template>
  </el-dialog>
  <div class="lookALlBusiness">
	<el-dialog
    v-model="dialogLookALlBusiness"
    width="800px"
	style="padding:20px 0;"
	:lock-scroll="false"
		top="10vh"
		@close="openFirstDialog"
  >
  <template #title>
    {{secondDialogTitle}}
  </template>
  <div style="background-color:#F5F5F5;height:20px;line-height:20px;margin-top:15px;padding:10px" v-if="secondDialogTitle!=='采购过'"><span style="color:red">*</span>提供优质配送及专属履约服务，建议全选</div>
  <div class="business">
	<div class="business-no-checked">
		<div class="business-no-checked-top">
			<PartTitle title="未选商家" />
			<div>
				<el-checkbox v-model="isCheckedAllBusiness" label="全选" size="large" @change="selecteAllBusiness"/>
			</div>
		</div>
		<div class="business-no-checked-body">
			<div class="business-no-checked-body-top">
				<el-icon><Search /></el-icon>
				<el-input   v-model = "searchNoSelectedBusinessName" type="text" placeholder="请输入店铺名称" style="border:none;background-color: #f5f5f5;width: 100%;margin-left:5px" @input="searchByName(1)" />
			</div>
			<div class="business-no-checked-body-body">
				<el-checkbox-group v-model="noCheckedBusinessList" @change="selectBusiness">
					<el-checkbox v-for="item in noCheckedBusiness1" :key="item.id" :label="item.shopCode" class="checkboxItem" >
							<span class="checkboxText">{{ item.showName }}</span>&nbsp;
						</el-checkbox>

  </el-checkbox-group>
			</div>
		</div>
		
	</div>
	<div class="business-no-checked" style="margin-right:0px;">
		<div class="business-no-checked-top">
			<PartTitle title="已选商家" />
			<div style="cursor: pointer;color:#00B955" @click="emptySelectedBusiness">
				<el-icon><Delete /></el-icon><span style="vertical-align: 2px;margin-left:4px;">清空</span>
			</div>

		</div>
		<div class="business-no-checked-body" >
			<div class="business-no-checked-body-top">
				<el-icon><Search /></el-icon>
				<el-input v-model="searchSelectedBusinessByName" type="text" placeholder="请输入店铺名称" style="border:none;background-color: #f5f5f5;margin-left:5px;width: 100%;" @input="searchByName(2)" />
			</div>
			<div class="business-no-checked-body-body">
				<el-checkbox-group @change="cancelSelectBusiness">
					<el-checkbox v-for="item in ckeckedBusiness1" :key="item.id" :label="item.shopCode" class="checkboxItem" indeterminate>
							<span class="checkboxText">{{ item.showName }}</span>&nbsp;
						</el-checkbox>
  				</el-checkbox-group>
			</div>
		</div>
		
	</div>
	
  </div>
  <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="saveSelected">保存</el-button>
      </span>
    </template>
</el-dialog>
	
  </div>
  
	</div>
</template>

<script setup>
import PartTitle from '../../components/partTitle.vue';
import { ref, onMounted, watch, computed } from 'vue';
import { matchPriceRuleShopList, saveMatchRule, getProcureMatchRule,inquireMatchPriceResult,submitMatchPrice } from '@/http_pc/api';
import { ElMessage } from 'element-plus'

	const props = defineProps({
		showRuleVis: {
			default: false
		},
		defalutRule: {
			default: {}
		},
		// cancelDialog: {
		// 	type: Function,
		// 	default: () => {}
		// },
		handleOkRule: {
			type: Function,
			default: () => {}
		},
	});
	const useHistory = ref(false)
	const useGoodChain = ref(false)
	const useHighQuality = ref(false)
	const useOther = ref(false)
	const zeroOne = ref(false)
	const zeroTwo = ref(false)
	const zeroThree = ref(false)
	const zeroFour = ref(false)
	const OTHER_SHOP_CODE = ref([])
	const OTHER_SHOP = ref([])
	const GOOD_CHAIN_SHOP_CODE = ref([])
	const BEFORE_SHOP_CODE = ref([])

	const HIGH_QUALITY_SHOP_CODE = ref([])
	const secondDialogTitle = ref('')
	const BEFORE_SHOP = ref([])
	const showBEFORE_SHOP = ref([])
	const GOOD_CHAIN_SHOP = ref([])
	const HIGH_QUALITY_SHOP = ref([])
	const activityType = ref(1);
	const dialogLookALlBusiness = ref(false);
	const searchSelectedBusinessByName = ref('')
	const searchNoSelectedBusinessName = ref('')
	const noCheckedBusinessList = ref([])
	const noCheckedBusiness = ref([]);
	const noCheckedBusiness1 = ref([])
	const allBusiness = ref([])
	const ckeckedBusiness =ref([])
	const ckeckedBusiness1 =ref([])
	const promotionType = ref(0);
	const ruleArr = ref([]);
	const nearEffectDay = ref(12);
	// 自营店铺的字段
	const shopList = ref([]);
	const isIndeterminate = ref(false);
	const shopCodes = ref([]);
	const allShopsCode = ref([]);
	// pop店铺的字段
	const popShopList = ref([]);
	const checkAllPop = ref(true);
	const isIndeterminatePop = ref(false);
	const popShopCodes = ref([]);
	const allPopShopsCode = ref([]);
	const foldData = ref({
		one: false,
		two: false,
		three: false
	})
	const openFirstDialog = ()=>{
		emit('openFirstDialog')
	}
	const checkGOOD_CHAIN_SHOPItem = (value)=>{
		GOOD_CHAIN_SHOP.value.forEach(item=>{
			item.shopCode == value? item.selectStatus = 1:''
		})
	}
	const checkHIGH_QUALITY_SHOPItem = (value)=>{
		HIGH_QUALITY_SHOP.value.forEach(item=>{
			item.shopCode == value? item.selectStatus = 1:''
		})
	}
	const checkBEFORE_SHOPItem = (value)=>{
		BEFORE_SHOP.value.forEach(item=>{
			item.shopCode == value? item.selectStatus = 1:''
		})
	}
	
	const saveSelected = ()=>{
		if(secondDialogTitle.value === '采购过'){
			BEFORE_SHOP.value.forEach(item=>{
				item.selectStatus = noCheckedBusinessList.value.includes(item.shopCode)?1:0
			})
			BEFORE_SHOP_CODE.value = noCheckedBusinessList.value
		}else if(secondDialogTitle.value === '品质商家'){
			HIGH_QUALITY_SHOP.value.forEach(item=>{
				item.selectStatus = noCheckedBusinessList.value.includes(item.shopCode)?1:0
			})
			HIGH_QUALITY_SHOP_CODE.value = noCheckedBusinessList.value
		}else if(secondDialogTitle.value === '连锁好店'){
			GOOD_CHAIN_SHOP.value.forEach(item=>{
				item.selectStatus = noCheckedBusinessList.value.includes(item.shopCode)?1:0
			})
			GOOD_CHAIN_SHOP_CODE.value = noCheckedBusinessList.value
		}
		else{
			OTHER_SHOP.value.forEach(item=>{
			    item.selectStatus = noCheckedBusinessList.value.includes(item.shopCode)?1:0
			})
			OTHER_SHOP_CODE.value = noCheckedBusinessList.value
		}
		dialogLookALlBusiness.value = false
		emit('openFirstDialog')
		
	}
	const searchByName = (type)=>{
		if(type==1){
			noCheckedBusiness1.value = noCheckedBusiness.value.filter(item=>item.showName.includes(searchNoSelectedBusinessName.value))
		}else{
			 ckeckedBusiness1.value = ckeckedBusiness.value.filter(item=>item.showName.includes(searchSelectedBusinessByName.value)&&noCheckedBusinessList.value.includes(item.shopCode))
			}	
	}
	const emptySelectedBusiness = ()=>{
		isCheckedAllBusiness.value = false
		ckeckedBusiness1.value = []
		noCheckedBusinessList.value = []
	}
	const selecteAllBusiness = (val) => {
		noCheckedBusinessList.value = val ? allBusiness.value : []

		ckeckedBusiness1.value = val ? noCheckedBusiness1.value:[]
	}
	const selectBusiness = (value) => {
		isCheckedAllBusiness.value = value.length === allBusiness.value.length
	
		ckeckedBusiness1.value = noCheckedBusiness1.value.filter(item=>{
			return value.includes(item.shopCode)
		})
	}
	const cancelSelectBusiness = (value)=>{
		console.log(value)
		ckeckedBusiness1.value = ckeckedBusiness1.value.filter(item=>{
		    return item.shopCode != value
		})
		noCheckedBusinessList.value = noCheckedBusinessList.value.filter(item=>item!=value)
		isCheckedAllBusiness.value = value.length === allBusiness.value.length
	}
	const lookBEFORE_SHOP = (event)=>{
		event.preventDefault();
		dialogLookALlBusiness.value = true
		secondDialogTitle.value = '采购过'
		noCheckedBusiness.value =  BEFORE_SHOP.value
		noCheckedBusiness1.value =  BEFORE_SHOP.value
		noCheckedBusinessList.value = BEFORE_SHOP_CODE.value

			ckeckedBusiness1.value =  BEFORE_SHOP.value.filter(item=>noCheckedBusinessList.value.includes(item.shopCode))
		
		allBusiness.value =  BEFORE_SHOP.value.map(item=>item.shopCode)

		ckeckedBusiness.value =  BEFORE_SHOP.value
	}
	const lookGOOD_CHAIN_SHOP = (event)=>{
		event.preventDefault();
		dialogLookALlBusiness.value = true
		secondDialogTitle.value = '连锁好店'
		noCheckedBusiness.value =  GOOD_CHAIN_SHOP.value
		noCheckedBusiness1.value =  GOOD_CHAIN_SHOP.value
		noCheckedBusinessList.value = GOOD_CHAIN_SHOP_CODE.value
		ckeckedBusiness1.value =  GOOD_CHAIN_SHOP.value.filter(item=>noCheckedBusinessList.value.includes(item.shopCode))
		allBusiness.value =  GOOD_CHAIN_SHOP.value.map(item=>item.shopCode)
		ckeckedBusiness.value =  GOOD_CHAIN_SHOP.value
	}
	const lookOTHER_SHOP = (event)=>{
		event.preventDefault();
		dialogLookALlBusiness.value = true
		secondDialogTitle.value = '其他商家'
		noCheckedBusiness.value =  OTHER_SHOP.value
		noCheckedBusiness1.value =  OTHER_SHOP.value
		noCheckedBusinessList.value = OTHER_SHOP_CODE.value
		ckeckedBusiness1.value =  OTHER_SHOP.value.filter(item=>noCheckedBusinessList.value.includes(item.shopCode))
		allBusiness.value =  OTHER_SHOP.value.map(item=>item.shopCode)
		ckeckedBusiness.value =  OTHER_SHOP.value
	}
	const lookHIGH_QUALITY_SHOP = (event) =>{
		event.preventDefault();
		dialogLookALlBusiness.value = true
		secondDialogTitle.value = '品质商家'
		noCheckedBusiness.value =  HIGH_QUALITY_SHOP.value
		noCheckedBusiness1.value =  HIGH_QUALITY_SHOP.value
		noCheckedBusinessList.value = HIGH_QUALITY_SHOP_CODE.value
		ckeckedBusiness1.value =  HIGH_QUALITY_SHOP.value.filter(item=>noCheckedBusinessList.value.includes(item.shopCode))
		allBusiness.value =  HIGH_QUALITY_SHOP.value.map(item=>item.shopCode)
		ckeckedBusiness.value =  HIGH_QUALITY_SHOP.value
	}
	const lookAllMerchantMethod = (even,type,showName)=>{
		console.log(even,type,showName)
		even.preventDefault();
		dialogLookALlBusiness.value = true
		showRuleVis.value = false;
		let MerchantList = type == 0 ? BEFORE_SHOP.value : type == 1 ? GOOD_CHAIN_SHOP.value : HIGH_QUALITY_SHOP.value
		MerchantList = MerchantList.filter(item=>item.showName == showName)
		console.log(MerchantList,'11')
		noCheckedBusiness.value = MerchantList
		noCheckedBusiness1.value = MerchantList
		noCheckedBusinessList.value = MerchantList.map(item=>item.shopCode)
		allBusiness.value = MerchantList.map(item=>item.shopCode)
		ckeckedBusiness1.value = MerchantList
		ckeckedBusiness.value = MerchantList
	}
	const emit = defineEmits(["cancelDialog",'openFirstDialog','changeMatchInfo']);
	// // 子组件定义自己的visible
	const showRuleVis = computed({
		get: () => props.showRuleVis,
		set: (value) => emit("cancelDialog", value),
	})
	const isAllSelectedRecMerchant = computed(()=>{
	    return (GOOD_CHAIN_SHOP_CODE.value.length+HIGH_QUALITY_SHOP_CODE.value.length+BEFORE_SHOP_CODE.value.length == HIGH_QUALITY_SHOP.value.length + GOOD_CHAIN_SHOP.value.length + BEFORE_SHOP.value.length)&& GOOD_CHAIN_SHOP_CODE.value.length+HIGH_QUALITY_SHOP_CODE.value.length+BEFORE_SHOP_CODE.value.length>0
	})
	const isCheckedAllBusiness = computed(()=>noCheckedBusinessList.value.length == noCheckedBusiness1.value.length&&noCheckedBusinessList.value.length>0)
	const checkAll = computed(()=>shopList.value.length == shopCodes.value.length&&shopCodes.value.length>0)
	
	
	const handleClose = () => {
		showRuleVis.value = false;
	}

	// 自营店铺checkbox的change事件
	const checkAllShopsChange = (val) => {
		shopCodes.value = val ? allShopsCode.value : [];
		isIndeterminate.value = false;
	}
	const checkShopChange = (value) => {
		const checkedCount = value.length
		checkAll.value = checkedCount === allShopsCode.value.length;
		isIndeterminate.value = checkedCount > 0 && checkedCount < allShopsCode.value.length;
	}

	// pop店铺checkbox的change事件
	const checkAllPopShopsChange = (val) => {
		zeroOne.value = val
		zeroTwo.value = val
		zeroThree.value = val
		zeroFour.value = val
		if(val){
			HIGH_QUALITY_SHOP_CODE.value = HIGH_QUALITY_SHOP.value.map(i => i.shopCode);
			GOOD_CHAIN_SHOP_CODE.value = GOOD_CHAIN_SHOP.value.map(i => i.shopCode);
			BEFORE_SHOP_CODE.value = BEFORE_SHOP.value.map(i => i.shopCode);
			//OTHER_SHOP_CODE.value = OTHER_SHOP.value.map(i => i.shopCode);
		}else{
			HIGH_QUALITY_SHOP_CODE.value = [];
			GOOD_CHAIN_SHOP_CODE.value = [];
			BEFORE_SHOP_CODE.value = [];
			//OTHER_SHOP_CODE.value = [];
		}
	}
	const checkPopShopChange = (value) => {
		console.log(value)
		const checkedCount = value.length
		checkAllPop.value = checkedCount === allPopShopsCode.value.length
		isIndeterminatePop.value = checkedCount > 0 && checkedCount < allPopShopsCode.value.length;
	}

	const getList = () => {
		/* matchPriceRuleShopList({}).then((res) => {
			shopList.value = res.result.shopList || [];
			allShopsCode.value = shopList.value.map(i => i.shopCode);
			//shopCodes.value = allShopsCode.value;

			popShopList.value = res.result.popShopList || [];
			allPopShopsCode.value = popShopList.value.map(i => i.shopCode);
			//popShopCodes.value = allPopShopsCode.value;
		}) */
		getProcureMatchRule().then(res => {
			if (res.code == 0) {
			
			
			
				useHistory.value = res.result.popShopMap.useHistory;
				useGoodChain.value = res.result.popShopMap.useGoodChain;
				useHighQuality.value = res.result.popShopMap.useHighQuality;
				useOther.value  = res.result.popShopMap.useOther;

				nearEffectDay.value = res.result.nearEffectDay;
				
				shopList.value = res.result.shopList || [];

				

			

			

				allShopsCode.value = shopList.value.map(i => i.shopCode);
				BEFORE_SHOP.value = res.result.popShopMap.historyList
				BEFORE_SHOP.value.forEach(item=>{
				    if(item.selectStatus==1)
					BEFORE_SHOP_CODE.value.push(item.shopCode)
				})
				GOOD_CHAIN_SHOP.value = res.result.popShopMap.goodChainList
				GOOD_CHAIN_SHOP.value.forEach(item=>{
				    if(item?.selectStatus==1)
					GOOD_CHAIN_SHOP_CODE.value.push(item.shopCode)
				})
				HIGH_QUALITY_SHOP.value = res.result.popShopMap.highQualityList
				HIGH_QUALITY_SHOP.value.forEach(item=>{
				    if(item.selectStatus==1)
					HIGH_QUALITY_SHOP_CODE.value.push(item.shopCode)
				})
				OTHER_SHOP.value = res.result.popShopMap.otherList
				OTHER_SHOP.value.forEach(item=>{
				    if(item?.selectStatus==1)
					OTHER_SHOP_CODE.value.push(item.shopCode)
				})
				popShopList.value = BEFORE_SHOP.value.concat(HIGH_QUALITY_SHOP.value,GOOD_CHAIN_SHOP.value)|| [];
				allPopShopsCode.value = popShopList.value.map(i => i.shopCode);
				const popCodes = [];
				const codes = [];
				popShopList.value.forEach((item) => {
					if (item?.selectStatus) {
						popCodes.push(item.shopCode)
					}
				})
				res.result.shopList.forEach((item) => {
					if (item?.selectStatus) {
						codes.push(item.shopCode)
					}
				})
				shopCodes.value = codes;
				popShopCodes.value = popCodes;
				checkAll.value = shopCodes.value.length == allShopsCode.value.length;
				checkAllPop.value = popShopCodes.value.length == allPopShopsCode.value.length;
				promotionType.value = res.result.promotionType;
				if (res.result.enableNearEffect) ruleArr.value.push(1);
				if (res.result.isCheckStock) ruleArr.value.push(2);
				if (res.result.isCheckLimit) ruleArr.value.push(3);
				if (res.result.isCheckMinOrder) ruleArr.value.push(4);
				if (res.result.isCheckTraceCode) ruleArr.value.push(5);
				emit("ruleTemp",ruleArr.value.length,
								promotionType.value,shopCodes.value.length,
								useHistory.value,
								useHighQuality.value,
								useGoodChain.value,
								useOther.value
							);
				let shopCodeCountMap = {};
				let resultArray = [];
				BEFORE_SHOP.value.forEach(item => {
  						const { showName } = item;
  						if (!shopCodeCountMap[showName]) {
    					shopCodeCountMap[showName] = 1;
  					} else {
    					shopCodeCountMap[showName]++;
  					}
				});
				BEFORE_SHOP.value.forEach(item => {
					const { showName } = item;
					const count = shopCodeCountMap[showName];
					if (count > 3 &&!resultArray.some(resultItem => resultItem.showName === showName)) {
						resultArray.push({...item, count });
					} else if (count <= 3) {
						resultArray.push({...item, count });
					}
				});
				showBEFORE_SHOP.value = resultArray;
			}
		})
	}
	const inputValue = (val) => {
		const re = /^[1-9]{0,1}([0-9])?$/;
		if (re.test(val)) {
			nearEffectDay.value = val;
		}
	}
	const handleOk = () => {
		const re = /^[1-9]([0-9])?$/;
		if (ruleArr.value.includes(1) && !re.test(nearEffectDay.value)) {
			ElMessage.error('效期只能输入两位以内的正整数');
			return;
		}
		saveMatchRule(getMatchRule('save')).then(res => {
		
			if (res.code == 0) {
				ElMessage.success('保存成功');
				emit("ruleTemp",ruleArr.value.length,
								promotionType.value,
								shopCodes.value.length,
								useHistory.value,
								useHighQuality.value,
								useGoodChain.value,
								useOther.value
							);
				setTimeout(() => {
					props.handleOkRule();
				}, 400);
					
					//getList()
			}
		})
		
	}

	const getMatchRule = (type) => {

		shopList.value.forEach((i) => {
			if (shopCodes.value.indexOf(i.shopCode) > -1) {
				i.selectStatus = 1;
			} else {
				i.selectStatus = 0;
			}
		});
	
		if(type=='save'){
			return{
			'shopCodeList': shopCodes.value,
			'popShopCodeMap': {
								'historyList':BEFORE_SHOP_CODE.value,
								'goodChainList':GOOD_CHAIN_SHOP_CODE.value,	
								'highQualityList':HIGH_QUALITY_SHOP_CODE.value,
								'otherList':OTHER_SHOP_CODE.value,
								'useHistory':useHistory.value,
								'useGoodChain':useGoodChain.value,
								'useHighQuality':useHighQuality.value,
								'useOther':useOther.value,
							},
			'enableNearEffect': ruleArr.value.includes(1) ? true : false,
			'nearEffectDay':  Number(nearEffectDay.value),
			'promotionType': promotionType.value,
			'isCheckStock': ruleArr.value.includes(2) ? true : false,
			'isCheckLimit': ruleArr.value.includes(3),
			'isCheckMinOrder': ruleArr.value.includes(4),
			'isCheckTraceCode': ruleArr.value.includes(5)
		}
		}
		const res = [...BEFORE_SHOP.value.filter(item=>BEFORE_SHOP_CODE.value.includes(item.shopCode)), ...GOOD_CHAIN_SHOP.value.filter(item=>GOOD_CHAIN_SHOP_CODE.value.includes(item.shopCode)), ...HIGH_QUALITY_SHOP.value.filter(item=>HIGH_QUALITY_SHOP_CODE.value.includes(item.shopCode))]
		return{
			'shopCodeList': shopList.value,
			'popShopCodeList': [...res],
			//matchPriceType: matchPriceType.value,
			'nearEffectDay': ruleArr.value.includes(1) ? Number(nearEffectDay.value) : null,
			'isCheckStock': ruleArr.value.includes(2) ? true : false,
			'isCheckLimit': ruleArr.value.includes(3),
			'isCheckMinOrder': ruleArr.value.includes(4),
			'isCheckTraceCode': ruleArr.value.includes(5)
		}
	}

	defineExpose({ getMatchRule,promotionType,shopCodes,BEFORE_SHOP_CODE,HIGH_QUALITY_SHOP_CODE,GOOD_CHAIN_SHOP_CODE,ruleArr });

	const initData = () => {
		ruleArr.value = [];
		if (props.defalutRule.isCheckStock) {
			ruleArr.value.push(2);
		}
		if (props.defalutRule.nearEffectDay) {
			ruleArr.value.push(1);
		}
		nearEffectDay.value = props.defalutRule.nearEffectDay;
		matchPriceType.value = props.defalutRule.matchPriceType;
		popShopList.value = props.defalutRule.popShopCodeList;
		shopList.value = props.defalutRule.shopCodeList;

		// 回显自营店铺各种回显状态
		allShopsCode.value = shopList.value.map(i => i.shopCode);
		shopCodes.value = [];
		shopList.value.forEach(i => {
			if(i.selectStatus) {
				shopCodes.value.push(i.shopCode)
			}
		});
		checkAll.value = shopCodes.value.length === allShopsCode.value.length;
		isIndeterminate.value = shopCodes.value.length > 0 && shopCodes.value.length < allShopsCode.value.length;

		// 回显pop店铺各种选中状态
		allPopShopsCode.value = popShopList.value.map(i => i.shopCode);
		popShopCodes.value = [];
		popShopList.value.forEach(i => {
			if(i.selectStatus) {
				popShopCodes.value.push(i.shopCode)
			}
		});
		checkAllPop.value = popShopCodes.value.length === allPopShopsCode.value.length;
		isIndeterminatePop.value = popShopCodes.value.length > 0 && popShopCodes.value.length < allPopShopsCode.value.length;
	}

	onMounted(() => {
		getList();
	})

	watch(
    () => props.showRuleVis,
    (val) => {
			if (val) {
				// 每次打开弹窗需要初始化回显
     		//initData();
			}
		},
		{
			immediate: true,
			deep: true,
		},
  );

</script>
<style scoped lang="scss">
:deep(.business-no-checked-body-top .el-input__wrapper) {
        box-shadow: none !important;
      
      
		padding:0px;

    }

   
.dialog-footer button:first-child {
  margin-right: 10px;
}
.tips {
	color: #9B9B9B;
}
.titleBox {
	margin-top: 18px;
	display: flex;
	align-items: center;
	font-weight: 14;
	.tipIcon {
		width: 14px;
		height: 14px;
		margin: 0 4px 0 20px;
	}
	span {
		display: flex;
		align-items: center;
		color: #DE7C13;
	}
}
.shopWrapper {
	margin-top: 12px;
	border-radius: 2px;
	border: 1px solid #E8E8E8;
	.shopTitle {
		padding: 12px 22px;
		background: #F5F5F5;
		border-bottom: 1px solid #E8E8E8;
		display: flex;
		justify-content: space-between;
		align-items: center ;
	}
	.shopContent {
		padding: 12px 22px;
		height: 105px;
		background: #FFFFFF;
		overflow-y: scroll !important;
	}
}
.checkboxText {
	color: #222222;
	margin-right:auto;
}
.checkboxItem {
	margin: 6px 10px;
}
::-webkit-scrollbar{
	display:none;
}
.business{
	display: flex;
	margin-top:18px;
	align-items: center;
	padding:0 20px;
}
.business-checked{
flex:1

}
.business-no-checked{
flex:1;
margin-right:20px;

}
.business-no-checked-top{
	display: flex;
	justify-content: space-between;
	height:32px;
	line-height:32px;
}
.business-checked-top{
	display: flex;
	justify-content: space-between;
	height:32px;
}
.business-checked-top span{
	color:#00B955
}
.business-checked-top span span{
	vertical-align: 2px;
	margin-left:5px;
}
.business-no-checked-body{
	border:1px solid #E8E8E8;

}
.business-checked-body-top{
	display: flex;
	border-bottom:1px solid #E8E8E8;
	background-color: #f5f5f5;
	height:43px;
	line-height: 43px;
	align-items: center;
}
.business-no-checked-body-top{
	display: flex;
	border-bottom:1px solid #E8E8E8;
	background-color: #f5f5f5;
	height:43px;
	line-height: 43px;
	align-items: center;
	padding-left:10px;
}
.business-no-checked-body-body{
	padding:18px;
	height:470px;
	overflow:auto;
}
.business-no-checked-body-body .el-checkbox{
	height:50px;
	line-height: 50px;
}
.business-checked-body-body{
	padding:18px;

	height:326px;
	overflow:auto;
}
.el-input__icon{
	background-color: #f5f5f5;
}
.input-with-Allselect{
	background-color: red;
}

</style>
<style>

 .lookALlBusiness  .el-dialog__title{
	color:#333333;
	font-weight: 600;
	font-size: 16px;
}
.main-body  .el-dialog__title{
	color:#333333;
	font-weight: 600;
	font-size: 16px;
}
.titleBox .title{
	font-size: 14px;
}
.lookALlBusiness .el-dialog__header{
	padding:0 20px;
}
.business-no-checked-body-top input{
	background-color: #f5f5f5;
}
 .el-checkbox__label{

}
.lookALlBusiness .el-dialog{
	height:760px;
}
.business-no-checked-body-body .el-checkbox{
    display: block;
}

</style>
<style>
.shopContent .el-checkbox-group{
	display: inline;
}

</style>