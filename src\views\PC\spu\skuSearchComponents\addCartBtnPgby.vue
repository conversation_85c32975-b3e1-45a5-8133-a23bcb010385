<!-- 加减购物车按钮，使用示例在temprow.vue组件 -->
<template>
  <div class="btn-container">
    <div class="content">
      <div class="spread-add">
        <div class="reduce" @click.stop.prevent="addProductCart('min')">-</div>
        <div class="cont">
          <input
            @click.stop.prevent
            class="input-val"
            :class="'input-val'+goodsId"
            type="tel"
            :value="productValue"
            @change="inputCart"
            @blur="handleCheckValue"
          />
        </div>
        <div class="plus" @click.stop.prevent="addProductCart('add')">+</div>
      </div>
      <div class="addPurchaseOrder soudOutBtn" v-if="productData.status === 2">已售罄</div>
      <!-- <div class="addPurchaseOrder pgbyBtn" v-else @click.stop.prevent="toJumpPage()">去抢购</div> -->
      <div class="addPurchaseOrder-pgby-box" v-else>
        <div class="addPurchaseOrder-addCar" v-if="productData.canAddToCart" @click.stop.prevent="addProductCart()">
          <img src="@/assets/images/addcar.png" alt="">
        </div>
        <div class="addPurchaseOrder-pgby pgbyBtn" @click.stop.prevent="toJumpPage()">去抢购</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { putRequest, changeCart } from '@/http_pc/api';
import { actionTracking } from '@/config/eventTracking';
import { onMounted, ref, watch, getCurrentInstance } from "vue";
import { ElMessage } from 'element-plus'
import { useStore } from "vuex";
import { formatDate } from '@/utils/index';
  const props = defineProps({
    productData: {
      default: {}
    },
    jgAgentInfo: {
      default: {}
    },
    keyWord: {
      default: "",
    },
    searchSortStrategyId: {
      default: ""
    },
    index: {
      default: ""
    },
  });
  const { proxy } = getCurrentInstance();

  const activePrice = ref('');
  const productValue = ref('');
  const is_split = ref('');
  const goodsId = ref('');
  const med_num = ref('');

  onMounted(() => {
    const actPgby = (props.productData || {}).actPgby || {};
    goodsId.value = (props.productData || {}).id;
    productValue.value = (props.productData || {}).cartProductNum ? (props.productData || {}).cartProductNum : actPgby.skuStartNum;
    is_split.value = (props.productData || {}).isSplit;
    med_num.value = parseInt((props.productData || {}).mediumPackageNum);
    activePrice.value = actPgby.minSkuPrice;
  })

  
  const addProductCart = (type) => {
    const actPgby = (props.productData || {}).actPgby || {};
    if (type === "add") {
      productValue.value += med_num.value;
      // this.addType = 3;
    } else if (type === "min") {
      if (productValue.value > 0) {
        if(productValue.value < actPgby.skuStartNum || productValue.value == actPgby.skuStartNum) {
          return;
        }
        productValue.value = is_split.value == 1 ? productValue.value - 1 : productValue.value - med_num.value;
        // this.addType = 1;
      }
    } else {
      postCartData(productValue.value);
    }
    // changePtPrice();
  }
  const getCartNum = () => {
    putRequest('get', '/merchant/center/cart/getCartNum', {
      r: Math.random(),
      num:0
    }).then((res) => {
      if(res.status === "success") {
        const numId = window.parent.document.getElementById('rigthCartNum');
        console.log('加购成功之后', numId);
        if (numId && res.num) {
          numId.className = 'topp cycle2';
          numId.innerText = res.num;
        }
      }
    })
  }
  const isPack = ref(false); // 套餐
  const store = useStore();
  const postCartData = (val) => {
    console.log('proxy.$merchantId', proxy.$merchantId);
    if (val > 0) {
      const config = isPack.value == true ? {
        'merchantId': proxy.$merchantId,
        'amount': val,
        "packageId": goodsId.value
      } : { 'merchantId': proxy.$merchantId, 'amount': val, "skuId": goodsId.value }
      // let jgInfo = JSON.parse(sessionStorage.getItem("jgInfo")) || null;
      // if (jgInfo) {
      //   jgInfo.direct = "1";
      //   config.mddata = JSON.stringify(jgInfo);
      // }
      putRequest('post', '/merchant/center/cart/changeCart.json', config).then((res) => {
        if (res.status == "success") {
          if (res.data.qty != val) {
            productValue.value = parseInt(res.data.qty);
          }
          
          if (res.dialog != null) {
            if (res.dialog.style == 20) {
              ElMessage.error(res.dialog.msg)
            } else {
              ElMessage.error(res.dialog.msg)
            }
          }
          if (res.data.message) {
            ElMessage.error(res.data.message);
          } else {
            // ElMessage.success('加购成功！')
            store.commit('app/changeprompt', { promptmsg: '加购成功！', showprompt: true })
            window.parent.postMessage({changeSubTotal: true}, '*');
            getCartNum();
          }

          // try {
          //   const addconfig = isPack.value == true ? {
          //     proid: goodsId.value,
          //     pronum: productValue.value,
          //     isAdd: 1,
          //     type: 1
          //   } : { proid: goodsId.value, pronum: productValue.value, isAdd: 1 }
          //   // window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
          // } catch (erro) {

          // }
          ;
        } else {
          productValue.value = 0;
          ElMessage.error(res.errorMsg || res.msg || res.dialog.msg);
        }
      }).catch((err) => {
        console.log('errrrrr', err);
        // WS.Bus.loading = false;
      })
    } else {
      const config = isPack.value == true ? { "packageIds": goodsId.value } : { "ids": goodsId.value }
      putRequest('post', `/app/batchRemoveProductFromCart`, config).then((res) => {
        if (res.status == "success") {
          productValue.value = 0;
          // try {
          //   const addconfig = isPack.value == true ? {
          //     proid: goodsId.value,
          //     pronum: 0,
          //     isAdd: 1,
          //     type: 1
          //   } : { proid: goodsId.value, pronum: 0, isAdd: 1 }
          //   window.webkit.messageHandlers.addPlanNumber.postMessage(addconfig);//ios
          // } catch (erro) {

          // }
          // ;
          // try {
          //   if (isPack.value == true) {
          //     window.hybrid.addPlanNumber(goodsId.value, 0, 1, 1); //android
          //   } else {
          //     window.hybrid.addPlanNumber(goodsId.value, 0, 1); //android
          //   }
          // } catch (erro) {

          // }
          // ;
        }
      })
    }
  }
  const inputCart = (e) => {
    let num = parseInt(e.target.value);
    num = num > 0 ? num : 0;
    productValue.value = num;
    // this.addType = 2;
  }
  const getPrice = (item) => {
    if (item.actPt) {
      if (item.actPt.stepPriceStatus == 1) {
        //阶梯\
        return item.actPt.minSkuPrice;
      } else if (item.actPt.stepPriceStatus == 2 && item.actPt.assembleStatus == 1) {
        return item.actPt.assemblePrice;
      }
      return ''
    } else if (item.actPgby) {
      return item.actPgby.assemblePrice;
    } else if (item.priceType == 2 && item.skuPriceRangeList) {
      return item.skuPriceRangeList[item.skuPriceRangeList.length -1].price;
    } else {
      return item.fob
    }
  }
  const handleCheckValue = (e) => {
    let num = parseInt(e.target.value);
    if (num < ((props.productData || {}).actPgby || {}).skuStartNum) {
      productValue.value = ((props.productData || {}).actPgby || {}).skuStartNum;
      return false;
    }
    if (is_split.value == 0) {
      const remainder = num % med_num.value;
      if (remainder > 0) {
        productValue.value = num - remainder;
      }
    }
  }
      
  const changePtPrice = () => {
    changeCart({
      skuId: (props.productData || {}).id,
      quantity: productValue.value,
      merchantId: proxy.$merchantId,
    }).then((res) => {
      console.log('加购', res);
      if (res.data.status === 'success') {
        activePrice.value = res.data.data.price;
        // proxy.$emit('change-price', (props.productData || {}).id, activePrice.value);
      } else {
        WS.Toast(res.data.errorMsg);
      }
    })
  }
  const toJumpPage = () => {
    let suixinPin = ((props.productData || {}).actPgby || {}).supportSuiXinPin || false
    window.open(`/merchant/center/order/group/settle.htm?productNum=${productValue.value}&skuId=${(props.productData || {}).id}&bizSource=10&shopCodes=${(props.productData || {}).shopCode}&isSupportOldSxp=${suixinPin}&isThirdCompany=${(props.productData || {}).isThirdCompany}`)
     //运营位埋点
    //  if(props.productData.operationId){
    //   actionTracking('pc_action_Product_Click', {
    //       commodityId: props.productData.id,
    //       commodityName:  props.productData.commonName,
    //       commodityCode:  props.productData.barcode,
    //       commodityCategory:  props.productData.categoryId,
    //       spid: props.trackInfo.spid,
    //       sid: props.trackInfo.sid,
    //       sptype: props.trackInfo.sptype,
    //       real: 2,
    //       search_sort_strategy_id: props.searchSortStrategyId,
    //       active_type: 0,
    //       click_item: 0,
    //       goods_groupid:  props.productData.operationExhibitionId,
    //       active_id:  props.productData.operationId,
    //       active_index: props.index
    //     })
    // }
  }
   

</script>

<style lang="scss" scoped>
div {
  box-sizing: border-box;
}

.btn-container {
  margin: 20px 0 10px 0;
  .addPurchaseOrder {
    cursor: pointer;
    width: 100px;
    height: 42px;
    margin-left: 10px;
    background-image: linear-gradient(90deg, #FEA527 0%, #FE5427 100%);
    border-radius: 2px;
    font-size: 16px;
    font-family: MicrosoftYaHei;
    text-align: center;
    color: #ffffff;
    line-height: 42px;
    
  }
  .addPurchaseOrder-pgby-box {
    flex: 1;
    display: flex;
    .addPurchaseOrder-addCar {
      width: 40px;
      height: 42px;
      background: #FFFFFF;
      border-radius: 6px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 24px;
        height: 24px;
      }
    }
    .addPurchaseOrder-pgby {
      flex: 1;
      cursor: pointer;
      height: 42px;
      margin-left: 8px;
      background-color:#FF6204;
      border-radius: 6px;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: center;
      color: #ffffff;
      line-height: 42px;
      padding: 0 7px;
      box-sizing: border-box;
      
    }
  }
  .pgbyBtn {
    background-color:#FF6204;
  }
  .soudOutBtn {
    background: #838A93;
  }
}

.content {
  height: 28px;
  display: flex;
  align-items: center;
}

.spread-add {
  display: flex;
  display: -webkit-flex;
  flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  box-sizing: border-box;

  .plus,
  .reduce {
    // width: 32px;
    width: 22px;
    height: 42px;
    line-height: 42px;
    color: #757575;
    text-align: center;
    background: #EFEFEF;
    cursor: pointer;
    font-size: 20px;
  }

  .plus {
    border-left: 1px solid #d7d7d7;
    // border-radius: 0px 2px 2px 0px;
    border-radius: 1px 6px 6px 1px;
  }

  .reduce {
    border-right: 1px solid #d7d7d7;
    // border-radius: 2px 0px 0px 2px;
    border-radius: 6px 1px 1px 6px;
  }

  .cont {
    width: 40px;
    .input-val {
      box-sizing: border-box;
      padding: 0;
      width: 100%;
      text-align: center;
      color: #292933;
      border: none;
      outline: none;
      font-size: 16px;
      font-family: MicrosoftYaHei;
      text-align: left;
      color: #333333;
      line-height: 42px;
      text-align: center;
    }
  }
}
</style>
