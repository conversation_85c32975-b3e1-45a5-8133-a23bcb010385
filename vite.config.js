import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'

const pathResolve = (dir) => {
  return resolve(__dirname, ".", dir)
}

const alias = {
  '@': pathResolve("src")
}

// https://vitejs.dev/config/
export default ({ command }) => {
  const prodMock = true;
  return {
    base: '/newstatic/',
    resolve: {
      alias
    },
    // 打包配置
    build: {
      target: 'es2015',
      outDir: 'dist', //指定输出路径
      // minify: 'terser', // 混淆器，terser构建后文件体积更小
      // terserOptions: {
      //   compress: {
      //     drop_console: process.env.NODE_ENV === 'production'
      //   }
      // },
    },
    plugins: [
      vue(),
      legacy({
        targets: ['defaults', 'ie >= 11', 'chrome 48'], //需要兼容的目标列表，可以设置多个
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      })
    ],
    server: {
      port: 3003,
      host: '0.0.0.0',
      open: true,
      proxy: { // 代理配置
        '/api': {
          // target: 'https://yapi.int.ybm100.com', // mock接口
          //target: 'https://new-app.test.ybm100.com/', // app代理接口域名
          target: 'https://new-www.test.ybm100.com/', // pc代理接口域名
          // target: 'http://localhost:8016/', // pc本地
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: proxy => {
            proxy.on("proxyReq", function (proxyReq, req) {
                    proxyReq.setHeader("Origin", "https://new-www.test.ybm100.com/")
                    proxyReq.setHeader(
                            "Cookie",
                            "uid=CgoUFGdWl+QuyejFILBEAg==; _abfpc=aeb1d3998ab403c91e918f976a7c5934dcb4549d_2.0; cna=2a0123c50d031e70e4e9f2c559c3975b; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; web_8b5e1b0f250a436e8c6af9871354bfba=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; crosSdkDT2019DeviceId=-gc0ooj--g4wuqq-xj1epyonqmiuth2-0jeecnz31; isDeviceUpload=1; Hm_lvt_9f4bfe0c69e174e92f282183ee72bed2=**********,**********,**********,**********; HMACCOUNT=4E0D0374F0AA052F; web_did=%7B%22did%22%3A%20%22389c2f9e-08a5-4282-8c87-1930de79dd53%22%7D; xyy_token=eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************.c2kpXlNIVFVEmqSDjN2qRH5CHCD39Z1LkTWpDSFZumMF_hF6x1hsmI3Um8DfafT2oXsOfcrC0ZjnMi55l66Hiw; xyy_principal=**********&YTNmYWQ2MGQyODZmODVjNDU1ODRmMzAwNDNiNzM0OGZlZWQ3Y2M0Mw&**********; xyy_last_login_time=*************; xyy=******************************; JSESSIONID=********************************; web_info=%7B%22sid%22%3A%***************%2C%22updated%22%3A%***************%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22new-www.test.ybm100.com%22%2C%22cuid%22%3A%20%220%22%7D; Hm_lpvt_9f4bfe0c69e174e92f282183ee72bed2=1750663379; qt_session=9XfHLg0e_1750663379750"
                    )
            })
          }
        }
      },
    },
    css: {
      // css预处理器
      preprocessorOptions: {
        scss: {
          charset: false,
          additionalData: `@use "./src/assets/scss/common.scss" as *;`,
          // additionalData: '@import "./src/assets/scss/common.scss";', // 添加公共样式
        },
      },
    },
  }
}
