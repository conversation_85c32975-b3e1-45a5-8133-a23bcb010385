import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'

const pathResolve = (dir) => {
  return resolve(__dirname, ".", dir)
}

const alias = {
  '@': pathResolve("src")
}

// https://vitejs.dev/config/
export default ({ command }) => {
  const prodMock = true;
  return {
    base: '/newstatic/',
    resolve: {
      alias
    },
    // 打包配置
    build: {
      target: 'es2015',
      outDir: 'dist', //指定输出路径
      // minify: 'terser', // 混淆器，terser构建后文件体积更小
      // terserOptions: {
      //   compress: {
      //     drop_console: process.env.NODE_ENV === 'production'
      //   }
      // },
    },
    plugins: [
      vue(),
      legacy({
        targets: ['defaults', 'ie >= 11', 'chrome 48'], //需要兼容的目标列表，可以设置多个
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      })
    ],
    server: {
      port: 3003,
      host: '0.0.0.0',
      open: true,
      proxy: { // 代理配置
        '/api': {
          // target: 'https://yapi.int.ybm100.com', // mock接口
          //target: 'https://new-app.test.ybm100.com/', // app代理接口域名
          target: 'https://new-www.test.ybm100.com/', // pc代理接口域名
          // target: 'http://localhost:8016/', // pc本地
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: proxy => {
            proxy.on("proxyReq", function (proxyReq, req) {
                    proxyReq.setHeader("Origin", "https://new-www.test.ybm100.com/")
                    proxyReq.setHeader(
                            "Cookie",
                            "qt_session=7ixEfKQq_1749026117143; cna=86c8bfe3f42ac754950ce4e8402d3aeb; Hm_lpvt_9f4bfe0c69e174e92f282183ee72bed2=1749025953; Hm_lvt_9f4bfe0c69e174e92f282183ee72bed2=1749022101,1749022453,1749022522,1749023611; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; web_did=%7B%22did%22%3A%20%22d00d5db7-4578-464d-8bec-2c2885dcd7a4%22%7D; web_info=%7B%22sid%22%3A%201749025889151%2C%22updated%22%3A%201749025889152%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%2C%22referrerDomain%22%3A%20%22new-www.test.ybm100.com%22%2C%22cuid%22%3A%20%220%22%7D; jg_login=0; xyy=******************************; xyy_last_login_time=1749025785309; xyy_principal=15********&NWRiMzMyYjE0OTQ5MWI4MDc2NTQ5ZTE0NDgxNjUzNDQwMDljZTQyYw&**********; xyy_token=eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************.Ua5kQ4bxauiZLz7tRDg3j9KTLXJstsl8i7CG-aK7lK_69lvjK7cOsu20QryvFPwWMlvSpJxZy8iBbqQUVzNgbA; isDeviceUpload=1; HMACCOUNT=E87B54048712D17D; web_8b5e1b0f250a436e8c6af9871354bfba=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; ssxmod_itna=iqjx07GQiQDti=Ki7diteiOGk8G2Qq=dDzxC5KG7DuxiK08D6mDB40Q9jwd8hocdNfrD7+rVY6qDsVKxiNDAxq0iDC8ejiKnZGDyGabUYhI3Yon0i7qr4stZi=oTvz2gPfwj1eGImnxibDBKDnhxDj=bqwDiiDDHwK+xDGD0rDDPDapmwitEG1QuYoQibwt+FcQRqhZTqaQGx4=GAdQi=PlcqKrKOeAr=aAYoEb+FFfwxYrb48=i1Q/7mb3PDXxik=4i1P4GCD0+msKf0GaMbj3vfd7GKYA2SffMtZWq8DHhxeA8PHQGKolG+ApqYD; ssxmod_itna2=iqjx07GQiQDti=Ki7diteiOGk8G2Qq=dDzxC5KG7DuxiK08D6mDB40Q9jwd8hocdNfrD7+rVYxD31dfaE2Y=NfD7p5iIbA0G3dQDGXxThak86ezTYzrg8AvUIiq=zLrzxhEhAkB=OuGTG3OIbUg0qI5FkGPCkQ/fODXFTIK3wr7In+R+CQcfjtXSeKffghiTkii2Q6n/k0nC7U2Fdh0nTKfbAc90lhyFMvi2uNTMmj5tMadqpER3dgPFq7tZP9e8GUEzmGgfhXrdL4XbWQ1dunbauHXrMMAyWRocG62aLQ4mlMgqUd29NbRQY5BDIgL1jjO=Puomi=DRC6vPf5QXDxN7iGh=6T6Ic2oGlIRlPQhv52E8Wow2LoFUMP3z0ba=DWcfdrGnlYzZ262QM=eXBt41IQpvIBP=GfPnfuCD2DEur5shA0D3XoIYiX3I0bgPxgv9C0dUjN4wNPo=cLRQ5x=QhtN0Zv2UU9Wc0RN6rX3ZAX7U5ot57N9aLMzRj6I3QvyhEv9QwhNKM5rSd/BG+6iSxwiDTjowAS5to7GotOmIZpQsiAi90lGpmrPErGehXaao5zgKiK=QQt26yCa=e6XnDZxQjExx2xScpXppv3TcplUKr71UYh5kh6IEr0/r9cO15fsYKQIFoK0QMfUB9Am8QOGo6R6+oSt/SAqs7D=jmyQG5QQEGP53sS63GN3D/Gx/3Er33iIbB9wEGq/5f7YbDlMy+v/D+CA3rqQDKfs+If4OAA/u5Qpyoegwre4VfoukIvxADRw7eqO4lkK2w3APsq1h6DKkSdirqBxIfTbhUGrixjD=A=CNIBvC0PAD+vsbAbmBrx/EnTCWDl2D8Du4OmmDgvUiNmrD+TQxijDnDtGCktDxQ+DKxKYoXu5BxnxzAxlxhLoYiSnDUTEW5nTqiDD; uid=rBQRYGguwz+uhhbtGUCkAg==; _abfpc=6eaa1b999fe5bdf9c2e8d3839e93ed33ad24b909_2.0; crosSdkDT2019DeviceId=-640q42--87wfkq-u2uhp6ahkmzduat-u90d050wd"
                    )
            })
          }
        }
      },
    },
    css: {
      // css预处理器
      preprocessorOptions: {
        scss: {
          charset: false,
          additionalData: `@use "./src/assets/scss/common.scss" as *;`,
          // additionalData: '@import "./src/assets/scss/common.scss";', // 添加公共样式
        },
      },
    },
  }
}
